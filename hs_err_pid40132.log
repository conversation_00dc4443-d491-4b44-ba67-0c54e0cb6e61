#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2071184 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=40132, tid=11896
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.10+7) (build 17.0.10+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.10+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -javaagent:C:\\Users\\<USER>\\.m2\\repository\\org\\jacoco\\org.jacoco.agent\\0.8.12\\org.jacoco.agent-0.8.12-runtime.jar=destfile=E:\\workspaces\\eastgate\\source\\smaile\\code\\SMAILE\\target\\jacoco.exec C:\Users\<USER>\AppData\Local\Temp\surefire13791098071339979188\surefirebooter-20250820090617095_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire13791098071339979188 2025-08-20T09-06-16_962-jvmRun1 surefire-20250820090617095_1tmp surefire_0-20250820090617095_2tmp

Host: Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug 20 09:06:19 2025 SE Asia Standard Time elapsed time: 1.937176 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001cfe03ac2b0):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=11896, stack(0x00000066afd00000,0x00000066afe00000)]


Current CompileTask:
C2:   1937 2186       4       org.jacoco.agent.rt.internal_aeaf9ab.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x00000066afd00000,0x00000066afe00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67d2c9]
V  [jvm.dll+0x834b1a]
V  [jvm.dll+0x8365de]
V  [jvm.dll+0x836c43]
V  [jvm.dll+0x2477ff]
V  [jvm.dll+0xac514]
V  [jvm.dll+0xacb5c]
V  [jvm.dll+0x3667d7]
V  [jvm.dll+0x330bfa]
V  [jvm.dll+0x33009a]
V  [jvm.dll+0x219ef1]
V  [jvm.dll+0x219311]
V  [jvm.dll+0x1a502d]
V  [jvm.dll+0x2290de]
V  [jvm.dll+0x2272ac]
V  [jvm.dll+0x7e9bd7]
V  [jvm.dll+0x7e3fda]
V  [jvm.dll+0x67c1b5]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001cfe022b3a0, length=16, elements={
0x000001cfece2adc0, 0x000001cf971a2030, 0x000001cf971a2eb0, 0x000001cf971b8a40,
0x000001cf971ba420, 0x000001cf971bde00, 0x000001cf971be7c0, 0x000001cf99bd2660,
0x000001cf99bd6470, 0x000001cf99bd8d90, 0x000001cf99bd0090, 0x000001cf99e64950,
0x000001cfdf1c0640, 0x000001cfdff2b6e0, 0x000001cfdfd5b510, 0x000001cfe03ac2b0
}

Java Threads: ( => current thread )
  0x000001cfece2adc0 JavaThread "main" [_thread_in_Java, id=18820, stack(0x00000066ade00000,0x00000066adf00000)]
  0x000001cf971a2030 JavaThread "Reference Handler" daemon [_thread_blocked, id=12472, stack(0x00000066ae500000,0x00000066ae600000)]
  0x000001cf971a2eb0 JavaThread "Finalizer" daemon [_thread_blocked, id=9676, stack(0x00000066ae600000,0x00000066ae700000)]
  0x000001cf971b8a40 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=37384, stack(0x00000066ae700000,0x00000066ae800000)]
  0x000001cf971ba420 JavaThread "Attach Listener" daemon [_thread_blocked, id=27444, stack(0x00000066ae800000,0x00000066ae900000)]
  0x000001cf971bde00 JavaThread "Service Thread" daemon [_thread_blocked, id=20848, stack(0x00000066ae900000,0x00000066aea00000)]
  0x000001cf971be7c0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=49400, stack(0x00000066aea00000,0x00000066aeb00000)]
  0x000001cf99bd2660 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=33056, stack(0x00000066aeb00000,0x00000066aec00000)]
  0x000001cf99bd6470 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=38708, stack(0x00000066aec00000,0x00000066aed00000)]
  0x000001cf99bd8d90 JavaThread "Sweeper thread" daemon [_thread_blocked, id=6976, stack(0x00000066aed00000,0x00000066aee00000)]
  0x000001cf99bd0090 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=39772, stack(0x00000066aee00000,0x00000066aef00000)]
  0x000001cf99e64950 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=45212, stack(0x00000066aef00000,0x00000066af000000)]
  0x000001cfdf1c0640 JavaThread "Notification Thread" daemon [_thread_blocked, id=23576, stack(0x00000066af000000,0x00000066af100000)]
  0x000001cfdff2b6e0 JavaThread "surefire-forkedjvm-stream-flusher" daemon [_thread_blocked, id=37224, stack(0x00000066afb00000,0x00000066afc00000)]
  0x000001cfdfd5b510 JavaThread "surefire-forkedjvm-command-thread" daemon [_thread_in_native, id=26140, stack(0x00000066afc00000,0x00000066afd00000)]
=>0x000001cfe03ac2b0 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=11896, stack(0x00000066afd00000,0x00000066afe00000)]

Other Threads:
  0x000001cf97199410 VMThread "VM Thread" [stack: 0x00000066ae400000,0x00000066ae500000] [id=27064]
  0x000001cfdf1c0b20 WatcherThread [stack: 0x00000066af100000,0x00000066af200000] [id=44708]
  0x000001cfeced6dd0 GCTaskThread "GC Thread#0" [stack: 0x00000066adf00000,0x00000066ae000000] [id=10912]
  0x000001cfdf8fb980 GCTaskThread "GC Thread#1" [stack: 0x00000066af200000,0x00000066af300000] [id=29060]
  0x000001cfdf6685d0 GCTaskThread "GC Thread#2" [stack: 0x00000066af300000,0x00000066af400000] [id=41248]
  0x000001cfe052e0f0 GCTaskThread "GC Thread#3" [stack: 0x00000066af400000,0x00000066af500000] [id=39144]
  0x000001cfe052e3b0 GCTaskThread "GC Thread#4" [stack: 0x00000066af500000,0x00000066af600000] [id=1220]
  0x000001cfdf8e48f0 GCTaskThread "GC Thread#5" [stack: 0x00000066af600000,0x00000066af700000] [id=37192]
  0x000001cfdf8e4bb0 GCTaskThread "GC Thread#6" [stack: 0x00000066af700000,0x00000066af800000] [id=37120]
  0x000001cfdf8e4e70 GCTaskThread "GC Thread#7" [stack: 0x00000066af800000,0x00000066af900000] [id=16344]
  0x000001cfdfd07310 GCTaskThread "GC Thread#8" [stack: 0x00000066af900000,0x00000066afa00000] [id=28320]
  0x000001cfdfd075d0 GCTaskThread "GC Thread#9" [stack: 0x00000066afa00000,0x00000066afb00000] [id=49452]
  0x000001cf96fc8290 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000066ae000000,0x00000066ae100000] [id=21004]
  0x000001cf96fc8cb0 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000066ae100000,0x00000066ae200000] [id=9588]
  0x000001cf970cfee0 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000066ae200000,0x00000066ae300000] [id=17888]
  0x000001cf970d0910 ConcurrentGCThread "G1 Service" [stack: 0x00000066ae300000,0x00000066ae400000] [id=45568]

Threads with active compile tasks:
C2 CompilerThread0     1981 2249 %     4       org.jacoco.agent.rt.internal_aeaf9ab.asm.SymbolTable::<init> @ 98 (536 bytes)
C2 CompilerThread1     1981 2041       4       org.jacoco.agent.rt.internal_aeaf9ab.asm.ClassReader::readCode (5117 bytes)
C2 CompilerThread2     1981 2186       4       org.jacoco.agent.rt.internal_aeaf9ab.asm.ClassReader::readMethod (1070 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000601000000, size: 8176 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001cf9a000000-0x000001cf9abb0000-0x000001cf9abb0000), size 12255232, SharedBaseAddress: 0x000001cf9a000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001cf9b000000-0x000001cfdb000000, reserved size: 1073741824
Narrow klass base: 0x000001cf9a000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8176M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 106185K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 25 young (102400K), 1 survivors (4096K)
 Metaspace       used 12447K, committed 12608K, reserved 1114112K
  class space    used 1382K, committed 1472K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601000000, 0x0000000601000000| Untracked 
|   1|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601400000, 0x0000000601400000| Untracked 
|   2|0x0000000601800000, 0x000000060193cc00, 0x0000000601c00000| 30%| O|  |TAMS 0x0000000601800000, 0x0000000601800000| Untracked 
|   3|0x0000000601c00000, 0x0000000601c00000, 0x0000000602000000|  0%| F|  |TAMS 0x0000000601c00000, 0x0000000601c00000| Untracked 
|   4|0x0000000602000000, 0x0000000602000000, 0x0000000602400000|  0%| F|  |TAMS 0x0000000602000000, 0x0000000602000000| Untracked 
|   5|0x0000000602400000, 0x0000000602400000, 0x0000000602800000|  0%| F|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|   6|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|   7|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|   8|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|   9|0x0000000603400000, 0x0000000603400000, 0x0000000603800000|  0%| F|  |TAMS 0x0000000603400000, 0x0000000603400000| Untracked 
|  10|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|  11|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|  12|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|  13|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|  14|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|  15|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|  16|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  17|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  18|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  19|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  20|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  21|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  22|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  23|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  24|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  25|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  26|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  27|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  28|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  29|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  30|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  31|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  32|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  33|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  34|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  35|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  36|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  37|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  38|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  39|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  40|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  41|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  42|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  43|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  44|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  45|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  46|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  47|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  48|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  49|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  50|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  51|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  52|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  53|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  54|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  55|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  56|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  57|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  58|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  59|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  60|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  61|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  62|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  63|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  64|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  65|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  66|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  67|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  68|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  69|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  70|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  71|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  72|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  73|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  74|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  75|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  76|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  77|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  78|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  79|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  80|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  81|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  82|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  83|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  84|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  85|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  86|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  87|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  88|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  89|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  90|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  91|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  92|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  93|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  94|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  95|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  96|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  97|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  98|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  99|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
| 100|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
| 101|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
| 102|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
| 103|0x000000061ac00000, 0x000000061ae00a78, 0x000000061b000000| 50%| E|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Complete 
| 104|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%| E|CS|TAMS 0x000000061b000000, 0x000000061b000000| Complete 
| 105|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%| E|CS|TAMS 0x000000061b400000, 0x000000061b400000| Complete 
| 106|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%| E|CS|TAMS 0x000000061b800000, 0x000000061b800000| Complete 
| 107|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%| E|CS|TAMS 0x000000061bc00000, 0x000000061bc00000| Complete 
| 108|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%| E|CS|TAMS 0x000000061c000000, 0x000000061c000000| Complete 
| 109|0x000000061c400000, 0x000000061c800000, 0x000000061c800000|100%| E|CS|TAMS 0x000000061c400000, 0x000000061c400000| Complete 
| 110|0x000000061c800000, 0x000000061cc00000, 0x000000061cc00000|100%| E|CS|TAMS 0x000000061c800000, 0x000000061c800000| Complete 
| 111|0x000000061cc00000, 0x000000061d000000, 0x000000061d000000|100%| E|CS|TAMS 0x000000061cc00000, 0x000000061cc00000| Complete 
| 112|0x000000061d000000, 0x000000061d400000, 0x000000061d400000|100%| E|CS|TAMS 0x000000061d000000, 0x000000061d000000| Complete 
| 113|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%| E|CS|TAMS 0x000000061d400000, 0x000000061d400000| Complete 
| 114|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%| E|CS|TAMS 0x000000061d800000, 0x000000061d800000| Complete 
| 115|0x000000061dc00000, 0x000000061de75ab0, 0x000000061e000000| 61%| S|CS|TAMS 0x000000061dc00000, 0x000000061dc00000| Complete 
| 116|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%| E|CS|TAMS 0x000000061e000000, 0x000000061e000000| Complete 
| 117|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%| E|CS|TAMS 0x000000061e400000, 0x000000061e400000| Complete 
| 118|0x000000061e800000, 0x000000061ec00000, 0x000000061ec00000|100%| E|CS|TAMS 0x000000061e800000, 0x000000061e800000| Complete 
| 119|0x000000061ec00000, 0x000000061f000000, 0x000000061f000000|100%| E|CS|TAMS 0x000000061ec00000, 0x000000061ec00000| Complete 
| 120|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%| E|CS|TAMS 0x000000061f000000, 0x000000061f000000| Complete 
| 121|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%| E|CS|TAMS 0x000000061f400000, 0x000000061f400000| Complete 
| 122|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%| E|CS|TAMS 0x000000061f800000, 0x000000061f800000| Complete 
| 123|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%| E|CS|TAMS 0x000000061fc00000, 0x000000061fc00000| Complete 
| 124|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%| E|CS|TAMS 0x0000000620000000, 0x0000000620000000| Complete 
| 125|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%| E|CS|TAMS 0x0000000620400000, 0x0000000620400000| Complete 
| 126|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%| E|CS|TAMS 0x0000000620800000, 0x0000000620800000| Complete 
| 127|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%| E|CS|TAMS 0x0000000620c00000, 0x0000000620c00000| Complete 

Card table byte_map: [0x000001cff5e90000,0x000001cff6e90000] _byte_map_base: 0x000001cff2e88000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001cfeced73f0, (CMBitMap*) 0x000001cfeced7430
 Prev Bits: [0x000001cff7e90000, 0x000001cfffe50000)
 Next Bits: [0x000001cf8f000000, 0x000001cf96fc0000)

Polling page: 0x000001cfeabf0000

Metaspace:

Usage:
  Non-class:     10.82 MB used.
      Class:      1.35 MB used.
       Both:     12.17 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      10.94 MB ( 17%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      12.38 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  4.36 MB
       Class:  14.44 MB
        Both:  18.80 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 138.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 198.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 582.
num_chunk_merges: 0.
num_chunk_splits: 366.
num_chunks_enlarged: 275.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1358Kb max_used=1358Kb free=118641Kb
 bounds [0x000001cf87ad0000, 0x000001cf87d40000, 0x000001cf8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=5419Kb max_used=5419Kb free=114580Kb
 bounds [0x000001cf80000000, 0x000001cf80550000, 0x000001cf87530000]
CodeHeap 'non-nmethods': size=5760Kb used=1175Kb max_used=1302Kb free=4584Kb
 bounds [0x000001cf87530000, 0x000001cf877a0000, 0x000001cf87ad0000]
 total_blobs=2744 nmethods=2273 adapters=383
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.873 Thread 0x000001cf99bd6470 2279       2       jdk.internal.org.objectweb.asm.Frame::getConcreteOutputType (111 bytes)
Event: 1.874 Thread 0x000001cf99bd6470 nmethod 2279 0x000001cf8053e310 code [0x000001cf8053e4c0, 0x000001cf8053e718]
Event: 1.874 Thread 0x000001cf99bd6470 2280       2       java.lang.PublicMethods$MethodList::<init> (10 bytes)
Event: 1.874 Thread 0x000001cf99bd6470 nmethod 2280 0x000001cf8053e890 code [0x000001cf8053ea20, 0x000001cf8053eb78]
Event: 1.874 Thread 0x000001cf99bd6470 2281       2       sun.reflect.annotation.AnnotationParser::annotationForMap (16 bytes)
Event: 1.874 Thread 0x000001cf99bd6470 nmethod 2281 0x000001cf8053ec10 code [0x000001cf8053edc0, 0x000001cf8053f078]
Event: 1.874 Thread 0x000001cf99bd6470 2282       2       sun.reflect.annotation.AnnotationParser$1::<init> (15 bytes)
Event: 1.874 Thread 0x000001cf99bd6470 nmethod 2282 0x000001cf8053f190 code [0x000001cf8053f320, 0x000001cf8053f4f8]
Event: 1.874 Thread 0x000001cf99bd6470 2283       2       sun.reflect.annotation.AnnotationInvocationHandler::<init> (71 bytes)
Event: 1.875 Thread 0x000001cf99bd6470 nmethod 2283 0x000001cf8053f590 code [0x000001cf8053f7a0, 0x000001cf8053fc58]
Event: 1.875 Thread 0x000001cf99bd6470 2284       2       org.apache.logging.log4j.core.util.ReflectionUtil::$jacocoInit (52 bytes)
Event: 1.875 Thread 0x000001cf99bd6470 nmethod 2284 0x000001cf8053ff10 code [0x000001cf805400e0, 0x000001cf80540658]
Event: 1.875 Thread 0x000001cf99bd6470 2285       2       org.apache.logging.log4j.util.PropertySource$Comparator::compare (23 bytes)
Event: 1.875 Thread 0x000001cf99bd6470 nmethod 2285 0x000001cf80540810 code [0x000001cf805409c0, 0x000001cf80540bf8]
Event: 1.876 Thread 0x000001cf99bd6470 2286       2       org.apache.logging.log4j.util.PropertySource$Comparator::compare (53 bytes)
Event: 1.876 Thread 0x000001cf99bd6470 nmethod 2286 0x000001cf80540d90 code [0x000001cf80540f80, 0x000001cf80541408]
Event: 1.876 Thread 0x000001cf99bd6470 2287       1       java.lang.reflect.Constructor::getSharedParameterTypes (5 bytes)
Event: 1.876 Thread 0x000001cf99bd6470 nmethod 2287 0x000001cf87c23390 code [0x000001cf87c23520, 0x000001cf87c235f8]
Event: 1.885 Thread 0x000001cf99bd6470 2289       1       java.util.concurrent.locks.AbstractOwnableSynchronizer::setExclusiveOwnerThread (6 bytes)
Event: 1.886 Thread 0x000001cf99bd6470 nmethod 2289 0x000001cf87c23690 code [0x000001cf87c23820, 0x000001cf87c23938]

GC Heap History (6 events):
Event: 0.425 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 524288K, used 24576K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 2789K, committed 2944K, reserved 1114112K
  class space    used 250K, committed 320K, reserved 1048576K
}
Event: 0.429 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 524288K, used 9398K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 2789K, committed 2944K, reserved 1114112K
  class space    used 250K, committed 320K, reserved 1048576K
}
Event: 0.621 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 524288K, used 29878K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 4323K, committed 4480K, reserved 1114112K
  class space    used 388K, committed 448K, reserved 1048576K
}
Event: 0.624 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 524288K, used 11044K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 4323K, committed 4480K, reserved 1114112K
  class space    used 388K, committed 448K, reserved 1048576K
}
Event: 1.144 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 524288K, used 56100K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 7802K, committed 7936K, reserved 1114112K
  class space    used 842K, committed 896K, reserved 1048576K
}
Event: 1.146 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 524288K, used 11977K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 7802K, committed 7936K, reserved 1114112K
  class space    used 842K, committed 896K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.005 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
Event: 0.019 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
Event: 0.055 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\instrument.dll
Event: 0.059 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\net.dll
Event: 0.061 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\nio.dll
Event: 0.063 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
Event: 0.188 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\verify.dll
Event: 0.230 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jimage.dll
Event: 0.405 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\management.dll
Event: 0.408 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\management_ext.dll

Deoptimization events (20 events):
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf87bf3b1c sp=0x00000066adef8330
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf875869a3 sp=0x00000066adef82f8 mode 2
Event: 1.310 Thread 0x000001cfece2adc0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001cf87bf3b1c relative=0x00000000000029bc
Event: 1.310 Thread 0x000001cfece2adc0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001cf87bf3b1c method=org.jacoco.agent.rt.internal_aeaf9ab.asm.tree.MethodNode.accept(Lorg/jacoco/agent/rt/internal_aeaf9ab/asm/MethodVisitor;)V @ 21 c2
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf87bf3b1c sp=0x00000066adef8330
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf875869a3 sp=0x00000066adef82f8 mode 2
Event: 1.310 Thread 0x000001cfece2adc0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001cf87bf3b1c relative=0x00000000000029bc
Event: 1.310 Thread 0x000001cfece2adc0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001cf87bf3b1c method=org.jacoco.agent.rt.internal_aeaf9ab.asm.tree.MethodNode.accept(Lorg/jacoco/agent/rt/internal_aeaf9ab/asm/MethodVisitor;)V @ 21 c2
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf87bf3b1c sp=0x00000066adef8330
Event: 1.310 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf875869a3 sp=0x00000066adef82f8 mode 2
Event: 1.674 Thread 0x000001cfece2adc0 Uncommon trap: trap_request=0xffffff6e fr.pc=0x000001cf87c0de34 relative=0x0000000000008394
Event: 1.674 Thread 0x000001cfece2adc0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x000001cf87c0de34 method=org.jacoco.agent.rt.internal_aeaf9ab.core.internal.flow.LabelFlowAnalyzer.markLabels(Lorg/jacoco/agent/rt/internal_aeaf9ab/asm/tree/MethodNode;)V @ 22 c2
Event: 1.674 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf87c0de34 sp=0x00000066adef82a0
Event: 1.674 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf875869a3 sp=0x00000066adef81b8 mode 2
Event: 1.756 Thread 0x000001cfece2adc0 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000001cf87bc56a4 relative=0x0000000000000204
Event: 1.756 Thread 0x000001cfece2adc0 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001cf87bc56a4 method=java.util.Arrays.copyOf([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; @ 35 c2
Event: 1.756 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf87bc56a4 sp=0x00000066adef8b80
Event: 1.756 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf875869a3 sp=0x00000066adef8b28 mode 2
Event: 1.877 Thread 0x000001cfece2adc0 DEOPT PACKING pc=0x000001cf8029f7a8 sp=0x00000066adef8580
Event: 1.877 Thread 0x000001cfece2adc0 DEOPT UNPACKING pc=0x000001cf87587143 sp=0x00000066adef7a60 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.908 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061f1bcc30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061f1bcc30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.923 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061f2a1250}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061f2a1250) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.935 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061f3cea38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061f3cea38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.944 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ece7278}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061ece7278) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.945 Thread 0x000001cfece2adc0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000061eceb1f0}: Found class java.lang.Object, but interface was expected> (0x000000061eceb1f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 0.951 Thread 0x000001cfece2adc0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000061ed5e018}: Found class java.lang.Object, but interface was expected> (0x000000061ed5e018) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 0.957 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ed96800}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000061ed96800) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.969 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ee07490}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061ee07490) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.006 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061e8027f0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061e8027f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.007 Thread 0x000001cfece2adc0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000061e805f20}: Found class java.lang.Object, but interface was expected> (0x000000061e805f20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 1.030 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ea78d88}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061ea78d88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.031 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061ea891d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061ea891d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.044 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061eb83a80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x000000061eb83a80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.076 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061e6eaef0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061e6eaef0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.130 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061e35b0b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x000000061e35b0b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.278 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061fc517c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x000000061fc517c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.283 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061fc8f008}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000061fc8f008) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.300 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061fe8e448}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object)'> (0x000000061fe8e448) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.301 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061fe91d98}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x000000061fe91d98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.672 Thread 0x000001cfece2adc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000061d2e5850}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long, int, java.lang.Object)'> (0x000000061d2e5850) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 0.586 Executing VM operation: HandshakeAllThreads
Event: 0.586 Executing VM operation: HandshakeAllThreads done
Event: 0.621 Executing VM operation: G1CollectForAllocation
Event: 0.624 Executing VM operation: G1CollectForAllocation done
Event: 0.798 Executing VM operation: HandshakeAllThreads
Event: 0.798 Executing VM operation: HandshakeAllThreads done
Event: 0.806 Executing VM operation: HandshakeAllThreads
Event: 0.806 Executing VM operation: HandshakeAllThreads done
Event: 0.818 Executing VM operation: HandshakeAllThreads
Event: 0.818 Executing VM operation: HandshakeAllThreads done
Event: 1.001 Executing VM operation: ICBufferFull
Event: 1.001 Executing VM operation: ICBufferFull done
Event: 1.144 Executing VM operation: G1CollectForAllocation
Event: 1.146 Executing VM operation: G1CollectForAllocation done
Event: 1.261 Executing VM operation: HandshakeAllThreads
Event: 1.261 Executing VM operation: HandshakeAllThreads done
Event: 1.310 Executing VM operation: HandshakeAllThreads
Event: 1.310 Executing VM operation: HandshakeAllThreads done
Event: 1.864 Executing VM operation: ICBufferFull
Event: 1.864 Executing VM operation: ICBufferFull done

Events (20 events):
Event: 1.709 loading class javax/naming/NamingException
Event: 1.709 loading class javax/naming/NamingException done
Event: 1.717 loading class java/util/DualPivotQuicksort
Event: 1.717 loading class java/util/DualPivotQuicksort done
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater done
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl done
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
Event: 1.725 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1 done
Event: 1.725 loading class sun/reflect/misc/ReflectUtil
Event: 1.726 loading class sun/reflect/misc/ReflectUtil done
Event: 1.734 loading class java/util/UUID
Event: 1.734 loading class java/util/UUID done
Event: 1.824 loading class java/time/temporal/UnsupportedTemporalTypeException
Event: 1.824 loading class java/time/DateTimeException
Event: 1.824 loading class java/time/DateTimeException done
Event: 1.824 loading class java/time/temporal/UnsupportedTemporalTypeException done
Event: 1.838 loading class java/util/regex/Pattern$BnM
Event: 1.838 loading class java/util/regex/Pattern$BnM done


Dynamic libraries:
0x00007ff78b580000 - 0x00007ff78b58e000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.exe
0x00007ff8bc670000 - 0x00007ff8bc868000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8ba690000 - 0x00007ff8ba752000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff8ba390000 - 0x00007ff8ba687000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8ba100000 - 0x00007ff8ba200000 	C:\Windows\System32\ucrtbase.dll
0x00007ff87d220000 - 0x00007ff87d23b000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\VCRUNTIME140.dll
0x00007ff87d240000 - 0x00007ff87d257000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jli.dll
0x00007ff8bb490000 - 0x00007ff8bb62d000 	C:\Windows\System32\USER32.dll
0x00007ff8b9ef0000 - 0x00007ff8b9f12000 	C:\Windows\System32\win32u.dll
0x00007ff89a3d0000 - 0x00007ff89a66a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8bc300000 - 0x00007ff8bc32b000 	C:\Windows\System32\GDI32.dll
0x00007ff8b9dd0000 - 0x00007ff8b9ee9000 	C:\Windows\System32\gdi32full.dll
0x00007ff8b9d30000 - 0x00007ff8b9dcd000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8bc330000 - 0x00007ff8bc3ce000 	C:\Windows\System32\msvcrt.dll
0x00007ff8badf0000 - 0x00007ff8bae1f000 	C:\Windows\System32\IMM32.DLL
0x00007ff883880000 - 0x00007ff88388c000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\vcruntime140_1.dll
0x00007ff851e40000 - 0x00007ff851ecd000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\msvcp140.dll
0x00007ff807aa0000 - 0x00007ff808702000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server\jvm.dll
0x00007ff8bc570000 - 0x00007ff8bc621000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8baba0000 - 0x00007ff8bac3f000 	C:\Windows\System32\sechost.dll
0x00007ff8bb300000 - 0x00007ff8bb426000 	C:\Windows\System32\RPCRT4.dll
0x00007ff8b9d00000 - 0x00007ff8b9d27000 	C:\Windows\System32\bcrypt.dll
0x00007ff8b9250000 - 0x00007ff8b929b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff8b5790000 - 0x00007ff8b579a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8a5690000 - 0x00007ff8a5699000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff8a52a0000 - 0x00007ff8a52c7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff8bb160000 - 0x00007ff8bb1cb000 	C:\Windows\System32\WS2_32.dll
0x00007ff8b90c0000 - 0x00007ff8b90d2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff8b7b60000 - 0x00007ff8b7b72000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff8834e0000 - 0x00007ff8834ea000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jimage.dll
0x00007ff8a3ac0000 - 0x00007ff8a3cc1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff888870000 - 0x00007ff8888a4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff8ba300000 - 0x00007ff8ba382000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff87f8e0000 - 0x00007ff87f8ee000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\instrument.dll
0x00007ff85e4c0000 - 0x00007ff85e4e5000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
0x00007ff851aa0000 - 0x00007ff851b77000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
0x00007ff8bb630000 - 0x00007ff8bbd9e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8b7d60000 - 0x00007ff8b8504000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff8ba840000 - 0x00007ff8bab93000 	C:\Windows\System32\combase.dll
0x00007ff8b9650000 - 0x00007ff8b967b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff8baf50000 - 0x00007ff8bb01d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff8bc3d0000 - 0x00007ff8bc47d000 	C:\Windows\System32\SHCORE.dll
0x00007ff8bc490000 - 0x00007ff8bc4eb000 	C:\Windows\System32\shlwapi.dll
0x00007ff8b9c30000 - 0x00007ff8b9c54000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff874c00000 - 0x00007ff874c18000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
0x00007ff87b640000 - 0x00007ff87b659000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\net.dll
0x00007ff8b3260000 - 0x00007ff8b336a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff8b9430000 - 0x00007ff8b949a000 	C:\Windows\system32\mswsock.dll
0x00007ff878690000 - 0x00007ff8786a6000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\nio.dll
0x00007ff882040000 - 0x00007ff882050000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\verify.dll
0x00007ff8a5650000 - 0x00007ff8a5667000 	C:\Windows\system32\napinsp.dll
0x00007ff8837b0000 - 0x00007ff8837cb000 	C:\Windows\system32\pnrpnsp.dll
0x00007ff883790000 - 0x00007ff8837ad000 	C:\Windows\system32\wshbth.dll
0x00007ff8b6db0000 - 0x00007ff8b6dcd000 	C:\Windows\system32\NLAapi.dll
0x00007ff8b90e0000 - 0x00007ff8b911b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff8b9120000 - 0x00007ff8b91ea000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ff8bc290000 - 0x00007ff8bc298000 	C:\Windows\System32\NSI.dll
0x00007ff883770000 - 0x00007ff883782000 	C:\Windows\System32\winrnr.dll
0x00007ff8b2320000 - 0x00007ff8b232a000 	C:\Windows\System32\rasadhlp.dll
0x00007ff8b28d0000 - 0x00007ff8b2950000 	C:\Windows\System32\fwpuclnt.dll
0x00007ff8af580000 - 0x00007ff8af589000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\management.dll
0x00007ff8a9db0000 - 0x00007ff8a9dbb000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\management_ext.dll
0x00007ff8bc480000 - 0x00007ff8bc488000 	C:\Windows\System32\PSAPI.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.10\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server

VM Arguments:
jvm_args: -javaagent:C:\\Users\\<USER>\\.m2\\repository\\org\\jacoco\\org.jacoco.agent\\0.8.12\\org.jacoco.agent-0.8.12-runtime.jar=destfile=E:\\workspaces\\eastgate\\source\\smaile\\code\\SMAILE\\target\\jacoco.exec 
java_command: C:\Users\<USER>\AppData\Local\Temp\surefire13791098071339979188\surefirebooter-20250820090617095_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire13791098071339979188 2025-08-20T09-06-16_962-jvmRun1 surefire-20250820090617095_1tmp surefire_0-20250820090617095_2tmp
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\surefire13791098071339979188\surefirebooter-20250820090617095_3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8573157376                                {product} {ergonomic}
   size_t MaxNewSize                               = 5142216704                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8573157376                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:/Users/<USER>/.jdks/corretto-17.0.10
PATH=C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\PuTTY;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;E:\workspaces\setup\apache-ant-1.10.14\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Git\cmd;C:\Program Files\Amazon\AWSCLIV2;C:\Program Files\WireGuard;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\mise\shims;C:\Program Files\nodejs;C:\Program Files\Docker\Docker\resources\bin;E:\workspaces\myspace\acli-rovo;C:\Program Files\Cloudflare\Cloudflare WARP;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\PowerShell\7;C:\Program Files (x86)\Scalefusion\Scalefusion MDM Agent\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin;C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\PuTTY;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Loc
USERNAME=Admin
TERM=xterm-256color
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 1:03 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xca, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 12 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32701M (3047M free)
TotalPageFile size 57964M (AvailPageFile size 8M)
current process WorkingSet (physical memory assigned to process): 226M, peak: 226M
current process commit charge ("private bytes"): 698M, peak: 702M

vm_info: OpenJDK 64-Bit Server VM (17.0.10+7-LTS) for windows-amd64 JRE (17.0.10+7-LTS), built on Jan 10 2024 22:11:07 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
