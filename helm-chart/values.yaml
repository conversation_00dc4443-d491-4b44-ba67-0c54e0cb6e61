container:
  # ALWAYS specify max heap size (in percentage of available mem)
  # Add other arguments if necessary
  java_opts: "-XX:MaxRAMPercentage=85"

  readinessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10

  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30

resources:
  requests:
    memory: 1Gi
    cpu: 512m

  limits:
    memory: 1Gi
    cpu: 1000m

service:
  port: 8000

keycloakUserId:
  claim: sub
  header: x-forwarded-smaile-user
contextPath: /api/v1
# Application configuration (this will override the config inside the docker image)
application:
  server:
    port: 8000
    servlet:
      context-path: /api/v1
  database:
    url: *****************************************************************************
    username: postgres
    password: pO5t$zum

  iam:
    endpoint: "http://keycloak.iam.svc.cluster.local:8080"
    realm: smaile
    client-id: smaile-be
    client-secret: B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB

# define the domain (it will be http://subDomain.baseDomain) that other service in the cluster can access to
# The domain can be replaced by your real domain and then it can be accessed on your internet
global:
  baseDomain: smaile.egs-dev.site
  subDomain: api

clusterDomain: svc.cluster.local

image:
  repository: acrsmailesoutheastasia001.azurecr.io
  tag: latest
  pullPolicy: IfNotPresent

cors:
  dev:
    allowOrigins:
      - regex: ".*"
    allowCredentials: true
    allowMethods:
      - GET
      - POST
      - OPTIONS
      - DELETE
      - OPTIONS
    maxAge: "86400s"
    allowHeaders:
      - Authorization
      - Content-Type
      - Accept
      - Origin
      - Cache-Control
      - Access-Control-Allow-Origin
      - Access-Control-Allow-Headers
      - priority
  prod:
    allowOrigins:
      - exact: "https://app.smaile.com"
    allowCredentials: true
    allowMethods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    maxAge: "3600s"
    allowHeaders:
      - Authorization
      - Content-Type
      - Accept
      - Origin
      - Cache-Control
      - Access-Control-Allow-Origin
      - Access-Control-Allow-Headers
      - priority
