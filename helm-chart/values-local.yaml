# The needed resource (RAM, CPU) 
container:
  java_opts: "-Xms512m -Xmx2048m"

  readinessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10

  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30

resources:
  requests:
    memory: 1Gi
    cpu: 512m

  limits:
    memory: 1Gi
    cpu: 1000m

# Application configuration (this will override the config inside the docker image)
application:
  server:
    port: 8000

  database:
    url: **************************************************
    username: postgres
    password: pO5t$zum

  iam:
    endpoint: http://localhost:8080
    realm: your-realm
    client-id: your-client
    client-secret: your-secret

# The needed secrets that already exists in this cluster
secret:
  database:
    name: scs-name--egs-database-secret

  keycloak:
    name: scs-name--egs-app-client-secret

# define the domain (it will be http://subDomain.baseDomain) that other service in the cluster can access to
# The domain can be replaced by your real domain and then it can be accessed on your internet
global:
  baseDomain: minikube
  subDomain: api.smaile

clusterDomain: svc.cluster.local

contextPath: /api/v1

image:
  repository:
  pullPolicy:

