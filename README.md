# SMAILE Health Core Service

An enterprise-grade Kubernetes-native health management application built with Spring Boot 3.5.4 featuring comprehensive pagination, RFC 7807 error handling, Redis caching, Liquibase database migrations, and OAuth2 authentication via Keycloak and oauth2-proxy.

## 🚀 Enterprise Features

### Core Technologies
- **Spring Boot 3.5.4** with Java 17
- **Zalando Problem** for RFC 7807 compliant error responses
- **Liquibase** for database schema versioning
- **Redis Caching** with Spring Cache abstraction
- **Spring Security OAuth2** Resource Server
- **PostgreSQL** with JPA/Hibernate
- **Micrometer Prometheus** for metrics
- **Comprehensive Audit Logging**

### Key Enterprise Capabilities

#### 📊 Pagination & Sorting
- Standardized pagination across all endpoints
- `PageResponse<T>` wrapper for consistent API responses
- Default page size: 20, configurable per request
- Multi-field sorting support

**Usage:**
```bash
# Get paginated organizations
GET /api/organizations/paged?page=0&size=10&sort=name,asc

# Response format
{
  "content": [...],
  "page": 0,
  "size": 10,
  "totalElements": 150,
  "totalPages": 15,
  "first": true,
  "last": false,
  "hasNext": true,
  "hasPrevious": false
}
```

#### 🛡️ Enterprise Error Handling
- RFC 7807 Problem Details compliance via Zalando Problem
- Structured error responses with correlation IDs
- Global exception handling with `@ControllerAdvice`
- Validation error mapping
- Comprehensive audit logging

**Error Response Format:**
```json
{
  "type": "https://smaile.health/problems/not-found",
  "title": "Resource Not Found", 
  "status": 404,
  "detail": "The requested resource could not be found"
}
```

#### 💾 Database Management
- **Liquibase** for schema versioning and migrations
- **Optimistic Locking** with `@Version` annotations
- **Database Indexes** for performance optimization
- **Audit Trails** with automatic timestamp tracking
- **Foreign Key Constraints** for data integrity

#### ⚡ Performance Optimization
- **Redis Caching** with configurable TTL (30min default, 60min production)
- **Connection Pooling** with HikariCP
- **JPA Query Optimization** with batch operations
- **Database Indexes** on frequently queried columns
- **Lazy Loading** for entity relationships

#### 🔐 Security Features
- **Method-Level Security** with `@PreAuthorize`
- **Role-Based Access Control** (RBAC)
- **Audit Logging** for all CRUD operations

#### 📈 Monitoring & Observability
- **Spring Boot Actuator** with custom health checks
- **Micrometer Prometheus** metrics
- **Structured Logging** with correlation IDs
- **Custom Metrics** for business operations
- **Health Checks** for Redis and PostgreSQL

### New API Endpoints

#### Paginated Endpoints
```bash
GET /api/organizations/paged    # Paginated organizations
GET /api/users/paged           # Paginated users  
GET /api/roles/paged           # Paginated roles
```

#### Monitoring Endpoints
```bash
GET /actuator/health           # Application health
GET /actuator/metrics          # Application metrics
GET /actuator/prometheus       # Prometheus metrics
```

## Architecture Overview

### Request Flow Diagram

```mermaid
graph TD
    A[Client Browser] --> B[Istio Gateway]
    B --> C[oauth2-proxy]
    C --> D{Authenticated?}
    D -->|No| E[Redirect to Keycloak]
    E --> F[Keycloak Login]
    F --> G[OAuth2 Callback]
    G --> C
    D -->|Yes| H[SMAILE Application]
    H --> I[PostgreSQL Database]
    
    subgraph "Kubernetes Cluster"
        B
        C
        H
        I
    end
    
    subgraph "Identity Provider"
        F
    end
```

### Component Architecture

- **Istio Gateway**: Entry point for all external traffic with TLS termination
- **oauth2-proxy**: Authentication middleware that handles OAuth2 flows
- **Keycloak**: Primary Identity Provider with user management and federation
- **SMAILE Application**: Spring Boot application with JWT token validation
- **PostgreSQL**: Database for application data and user session storage

### Hybrid User Management

The application implements a hybrid user management approach:
- **External Authentication**: Users authenticate via Keycloak (OIDC/OAuth2)
- **Internal Authorization**: Application manages roles and permissions internally
- **User Synchronization**: User profiles are synced from Keycloak to local database

## Support

For issues and questions:
- **Project Manager**: Ha Bui (<EMAIL>)
- **Development Team**: Tuan Nguyen (<EMAIL>), A Nguyen (<EMAIL>)
- **QA/BA**: B Nguyen (<EMAIL>)

Additional documentation available in the [wiki](wiki/README.md) directory.