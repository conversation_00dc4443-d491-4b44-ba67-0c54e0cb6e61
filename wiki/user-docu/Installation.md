# USER MANUAL

`Smaile version 1.0.0. Updated at: 08-Dec-2023`

## Table Of Contents

- [1. Introduction](#1-introduction)
- [2. Installation](#2-installation)
  - [2.1 Prerequisite](#21-prerequisite)
  - [2.2 Deploy to Docker container](#22-deploy-to-docker-container)
  - [2.3 Deploy on K8s cluster](#23-deploy-on-k8s-cluster)

## 1. Introduction

This document describes step-by-step how to deploy the application on your local machine using `Docker container` or on a `Kubernetes(k8s) cluster`.

## 2. Installation

### 2.1 Prerequisite

- A `MySQL database` is up and running (it should be accessible from the network of the Docker or K8s cluster)
- Install [JDK 17](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html)
- Install [Maven v3.6.3](https://maven.apache.org/install.html)
- Install and start [Docker](https://docs.docker.com/engine/install/)

To deploy on `K8s cluster`:

- Install [kubectl](https://kubernetes.io/docs/tasks/tools/#kubectl)
- A `K8s cluster` is up and running (For local use, the [minikube](https://kubernetes.io/docs/tasks/tools/#minikube) can be used)
- Install [helm](https://helm.sh/docs/intro/install/)
- Install [istio](https://istio.io/latest/docs/setup/getting-started/#download) to your cluster. To check if the `istio` is installed successfully?

![Istio service](../img/istio-svc.png)

**Note:**
- For `Window`, if the `minikube` is started with `--driver=docker`, we have to connect the local network with docker network using command:
```sh
minikube tunnel
```

- For `Ubuntu`, if the `minikube` is started with `--driver=none`, to access to the service in the cluster, it has to enable the `MetalLB minikube add-on`
```sh
minikube addons enable metallb

# Please run `minikube ip` to get the IP first
minikube addons configure metallb 
```

### 2.2 Deploy to Docker container

- From the project folder, run the command below to build the application:
```sh
mvn clean package -Dmaven.test.skip
```

- Build the docker image:
```sh
docker build --no-cache -f Dockerfile -t app-name:latest --build-arg server_port=8000 .
```

- Run the docker container:
```sh
docker run -p 8000:8000 app-name:latest
```

### 2.3 Deploy on K8s cluster

- Update the config in the `helm-chart/values-local.yaml` and `helm-chart/templates` if needed
- Execute the script `local-deploy.sh` to deploy the helm chart on local k8s.
