# USER MANUAL

`Smaile version 1.0.0. Updated at: 08-Dec-2023`

## Table Of Contents

- [1. Introduction](#1-introduction)
- [2. Configuration](#2-configuration)
  - [2.1 Database config](#21-database-config)
  - [2.2 IAM config](#22-iam-config)
- [3. Example](#3-example)

## 1. Introduction

This document describes configuration in `application.yml` that you need to know about in order to change it to fit your usage

## 2. Configuration

### 2.1. Database config

This section describes the configuration for the `MySQL database`.

```yaml
database:
  url: jdbc:mysql://<domain>:<port>/<database-name>
  username: <username>
  password: <password>
```

- `url:` The JDBC URL to connect to the `database`
- `username:` The username that will be used to access the database.
- `password:` The password of the username

### 2.2 IAM config

This section describes the configuration for the `Oauth2 authorization server`

```yaml
iam:
  endpoint: http://localhost:8080/realms/realm-name
  client-id: your-client
  client-secret: your-secret
```

- `endpoint:` The endpoint of the OAuth2 Authorization Server
- `client-id:` The client id
- `client-secret:` The client secret


## 3. Example

```yaml
database:
  url: ******************************************
  username:
  password:

iam:
  endpoint: http://localhost:8080/realms/realm-name
  client-id: your-client
  client-secret: your-secret
```
