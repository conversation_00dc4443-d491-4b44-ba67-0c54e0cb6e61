# CRUSH.md

## Build and Test Commands
```bash
# Build project
mvn clean package

# Run tests
mvn test

# Run single test class
mvn test -Dtest=UserServiceTest

# Generate coverage report
mvn clean test jacoco:report

# Run application locally
java -Dspring.profiles.active=dev -jar ./target/smaile-core-service-1.0.0.jar
```

## Code Style Guidelines
- **Package**: Use `com.smaile.health.*` (migrated from `com.smaile.endpoint`)
- **Imports**: Group by type, use static imports for constants
- **Annotations**: Lombok `@Getter/@Setter` on entities, `@RequiredArgsConstructor` on services/controllers
- **Naming**: camelCase for variables/methods, PascalCase for classes, UPPER_SNAKE_CASE for constants
- **Controllers**: Use `@RestController`, `@RequestMapping` with MediaType, `@PathVariable` for IDs
- **Services**: Interface + Impl pattern, `@Service` + `@Transactional(rollbackFor = Exception.class)`
- **Entities**: Extend `BaseEntity`, use `UUID` for IDs, JPA annotations, Lombok getters/setters
- **DTOs**: Use `@Valid` for validation, builder pattern where appropriate
- **Error Handling**: Custom exceptions with `@ResponseStatus`, use `NotFoundException` for missing resources
- **Security**: Use `@PreAuthorize` for method-level security, JWT-based authentication
- **Database**: Use Spring Data JPA repositories, Liquibase for migrations
- **Testing**: JUnit 5 + Mockito, H2 for test database, separate test profiles