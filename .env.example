# SMAILE Application Environment Variables Example
# Copy this file to .env and modify the values according to your environment

# =============================================================================
# CORS Configuration
# =============================================================================

# Global CORS Settings
CORS_ENABLED=true
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600

# Allowed Origins (add your frontend domains)
CORS_ALLOWED_ORIGIN_1=http://localhost:3000
CORS_ALLOWED_ORIGIN_2=http://localhost:8080
CORS_ALLOWED_ORIGIN_3=https://app.smaile.com
CORS_ALLOWED_ORIGIN_4=https://ui.x.com

# HTTP Methods
CORS_METHOD_GET=GET
CORS_METHOD_POST=POST
CORS_METHOD_PUT=PUT
CORS_METHOD_DELETE=DELETE
CORS_METHOD_OPTIONS=OPTIONS
CORS_METHOD_PATCH=PATCH
CORS_METHOD_HEAD=HEAD

# Headers
CORS_HEADER_AUTHORIZATION=Authorization
CORS_HEADER_CONTENT_TYPE=Content-Type
CORS_HEADER_ACCEPT=Accept
CORS_HEADER_ORIGIN=Origin
CORS_HEADER_CACHE_CONTROL=Cache-Control
CORS_HEADER_X_REQUESTED_WITH=X-Requested-With
CORS_HEADER_PRIORITY=priority

# Exposed Headers
CORS_EXPOSED_HEADER_LOCATION=Location
CORS_EXPOSED_HEADER_CONTENT_DISPOSITION=Content-Disposition
CORS_EXPOSED_HEADER_X_TOTAL_COUNT=X-Total-Count

# Path Patterns
CORS_PATH_PATTERN_1=/**
CORS_PATH_PATTERN_2=/api/**

# Development Environment CORS
CORS_DEV_ENABLED=true
CORS_DEV_ORIGIN_1=http://localhost:3000
CORS_DEV_ORIGIN_2=http://localhost:3001
CORS_DEV_ORIGIN_3=http://localhost:4200
CORS_DEV_ORIGIN_4=http://localhost:8080
CORS_DEV_ORIGIN_5=http://127.0.0.1:3000
CORS_DEV_ALLOW_CREDENTIALS=true
CORS_DEV_MAX_AGE=86400

# Staging Environment CORS
CORS_STAGING_ENABLED=true
CORS_STAGING_ORIGIN_1=https://staging.smaile.com
CORS_STAGING_ORIGIN_2=https://staging-ui.smaile.com
CORS_STAGING_ORIGIN_3=https://test.smaile.com
CORS_STAGING_ALLOW_CREDENTIALS=true
CORS_STAGING_MAX_AGE=7200

# Production Environment CORS
CORS_PROD_ENABLED=true
CORS_PROD_ORIGIN_1=https://app.smaile.com
CORS_PROD_ORIGIN_2=https://ui.x.com
CORS_PROD_ORIGIN_3=https://admin.smaile.com
CORS_PROD_ALLOW_CREDENTIALS=true
CORS_PROD_MAX_AGE=3600

# CORS Security Settings
CORS_VALIDATE_ORIGINS=true
CORS_LOG_REQUESTS=false
CORS_REJECT_NULL_ORIGIN=true
CORS_MAX_ALLOWED_ORIGINS=10

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_URL=***************************************
DATABASE_USERNAME=username
DATABASE_PASSWORD=password

# =============================================================================
# IAM Configuration
# =============================================================================
IAM_ENDPOINT=http://localhost:8080
IAM_REALM=smaile
IAM_CLIENT_ID=smaile-be
IAM_CLIENT_SECRET=your-client-secret-here

# =============================================================================
# Server Configuration
# =============================================================================
SERVER_PORT=8000

# =============================================================================
# Spring Profiles
# =============================================================================
SPRING_PROFILES_ACTIVE=dev

# =============================================================================
# Logging Configuration
# =============================================================================
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_SMAILE=DEBUG
LOGGING_LEVEL_SECURITY=DEBUG

# =============================================================================
# Management and Monitoring
# =============================================================================
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always

# =============================================================================
# Docker/Container Specific Variables
# =============================================================================
# Uncomment and modify these when running in containers

# CORS_PROD_ORIGIN_1=https://your-production-domain.com
# CORS_PROD_ORIGIN_2=https://your-admin-domain.com
# DATABASE_URL=**************************************
# IAM_ENDPOINT=http://keycloak:8080

# =============================================================================
# Kubernetes/Cloud Specific Variables
# =============================================================================
# Uncomment and modify these when deploying to Kubernetes/Cloud

# CORS_PROD_ORIGIN_1=https://app.yourdomain.com
# CORS_PROD_ORIGIN_2=https://admin.yourdomain.com
# DATABASE_URL=**********************************************
# IAM_ENDPOINT=https://iam.yourdomain.com

# =============================================================================
# Development Tools
# =============================================================================
# Additional origins for development tools

# Vite dev server
# CORS_DEV_ORIGIN_6=http://localhost:5173

# Webpack dev server
# CORS_DEV_ORIGIN_7=http://localhost:8081

# Angular dev server
# CORS_DEV_ORIGIN_8=http://localhost:4200

# Next.js dev server
# CORS_DEV_ORIGIN_9=http://localhost:3001

# =============================================================================
# Security Notes
# =============================================================================
# 1. Never commit this file with real secrets to version control
# 2. Use different values for each environment (dev, staging, prod)
# 3. In production, use HTTPS origins only
# 4. Limit the number of allowed origins for better security
# 5. Set CORS_LOG_REQUESTS=true only in development for debugging
# 6. Always validate origins in production (CORS_VALIDATE_ORIGINS=true)
# 7. Reject null origins in production (CORS_REJECT_NULL_ORIGIN=true)
