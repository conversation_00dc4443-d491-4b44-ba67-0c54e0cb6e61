# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

**Build the project:**
```bash
mvn clean package
```

**Run the application locally:**
```bash
java -Dspring.profiles.active=dev -jar ./target/smaile-core-service-1.0.0.jar
```

**Run tests:**
```bash
mvn test
```

**Run a single test class:**
```bash
mvn test -Dtest=ProjectResourceTest
```

**Generate code coverage report:**
```bash
mvn clean test jacoco:report
```

**Build Docker image:**
```bash
docker build -t smaile-core-service .
```

**Run with Docker Compose:**
```bash
docker-compose up -d
```

## Kubernetes Deployment Commands

**Deploy to Kubernetes:**
```bash
# Apply all manifests
kubectl apply -f helm-chart/templates/

# Deploy with <PERSON><PERSON>
helm install smaile ./helm-chart -n smaile --create-namespace

# Update deployment
helm upgrade smaile ./helm-chart -n smaile
```

**Debug Kubernetes deployment:**
```bash
# Check pod status
kubectl get pods -n smaile -l app=smaile-core-service

# View logs
kubectl logs -n smaile deployment/smaile-core-service -f

# Check Istio configuration
istioctl get virtualservice,gateway -n smaile

# Verify oauth2-proxy
kubectl logs -n smaile deployment/oauth2-proxy -f
```

## Architecture Overview

### Package Structure After Refactoring
The codebase recently underwent a major refactoring from `com.smaile.endpoint` to `com.smaile.health.core`. Key architectural components:

- **Application Entry Point**: `src/main/java/com/smaile/health/core/Application.java`
- **Configuration**: Spring Boot 3.5.4 with Java 17, OAuth2 security, PostgreSQL database
- **Security**: JWT-based authentication using Spring Security OAuth2 Resource Server
- **Database**: JPA/Hibernate with PostgreSQL, includes auditing capabilities
- **Internationalization**: Built-in i18n support with locale resolution
- **Mapping**: MapStruct for DTO-Entity mapping
- **Logging**: Log4j2 (Spring Boot logging excluded)

### Key Architectural Patterns

**Layered Architecture:**
- **Controllers** (`controller/`): REST endpoints with Spring MVC
- **Services** (`service/`): Business logic layer
- **Repositories** (`repository/`): Data access with Spring Data JPA and custom implementations
- **DTOs** (`dto/`): Data transfer objects with response wrappers
- **Entities** (`entity/`): JPA entities with auditing support
- **Mappers** (`mapper/`): MapStruct-based entity-DTO conversion

**Security Model:**
- OAuth2 JWT tokens with custom authentication converter
- Role-based access control with User/Role/Permission entities
- Security utilities for current user context access

**Database Design:**
- Auditable entities with created/modified timestamps and user tracking
- View entities for complex queries (e.g., `ProjectView`)
- Custom repository implementations for complex queries using predicates

### Test Structure
- **Integration Tests**: Base class `BaseIntegTest` with cleanup hooks
- **Test Package**: Tests still reference old package `com.smaile.smaileendpointservice` - needs updating
- **Mocking**: Mockito with Jupiter integration

### Configuration Notes
- **Profiles**: Supports `dev`, `test` profiles with environment-specific configs
- **Externalized Config**: Database and IAM settings via environment variables
- **Context Path**: `/api/v1` for all endpoints
- **File Upload**: Supports up to 100MB file uploads

### Package Scan Issue
The main Application class scans `com.smaile.endpoint` packages, but code is now in `com.smaile.health.core`. This needs to be updated for the application to work correctly.

## Deployment Architecture

### Kubernetes Environment
This application is designed to run in Kubernetes with the following stack:
- **Istio Service Mesh**: For traffic management, security, and observability
- **oauth2-proxy**: Handles OAuth2 authentication flow with Keycloak
- **Keycloak**: Primary Identity Provider for user authentication
- **PostgreSQL**: Database (can be in-cluster or external)

### Authentication Flow
1. **External Request** → Istio Gateway (TLS termination)
2. **Istio Gateway** → oauth2-proxy (authentication middleware)
3. **oauth2-proxy** → Keycloak (if not authenticated)
4. **Authenticated Request** → SMAILE Application (with JWT token)
5. **Application** → PostgreSQL (data persistence)

### Key Configuration Files
- `helm-chart/`: Helm charts for Kubernetes deployment
- `docker-compose.yml`: Local development environment
- `Dockerfile`: Container image definition
- `src/main/resources/application.yml`: Spring Boot configuration
- `src/main/resources/application-dev.yml`: Development profile settings

### Security Model
- **External Authentication**: Users authenticate via Keycloak (OIDC/OAuth2)
- **Internal Authorization**: Application manages roles/permissions in database
- **JWT Validation**: Spring Security validates tokens from Keycloak
- **Network Security**: Istio provides mTLS between services

### Environment Variables Required
- `DATABASE_URL`, `DATABASE_USERNAME`, `DATABASE_PASSWORD`: Database connection
- `IAM_ENDPOINT`, `IAM_REALM`, `IAM_CLIENT_ID`, `IAM_CLIENT_SECRET`: Keycloak configuration
- `SPRING_SECURITY_OAUTH2_RESOURCESERVER_JWT_ISSUER_URI`: JWT validation endpoint

### Common Issues to Watch For
1. **Package Scanning**: Application.java still references `com.smaile.endpoint` instead of `com.smaile.health.core`
2. **Test Package Names**: Test classes still use old package structure `com.smaile.smaileendpointservice`
3. **OAuth2 Configuration**: Ensure Keycloak client redirect URIs match oauth2-proxy configuration
4. **JWT Issuer URI**: Must exactly match Keycloak realm configuration for token validation