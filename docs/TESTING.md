# Testing Configuration Guide

This document explains the testing setup for the SMAILE Spring Boot application, including the H2 PostgreSQL-compatible in-memory database configuration and CORS testing.

## Overview

The test configuration provides:
- **H2 In-Memory Database** with PostgreSQL compatibility mode
- **CORS Configuration Testing** with simplified dev settings
- **Security Exclusions** for easier testing
- **Comprehensive Test Coverage** for database and CORS functionality

## Test Database Configuration

### H2 PostgreSQL Compatibility Mode

The test environment uses H2 database with PostgreSQL compatibility to ensure tests behave similarly to production:

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
    driverClassName: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create-drop
```

### Key Features

1. **PostgreSQL Mode**: `MODE=PostgreSQL` enables PostgreSQL syntax compatibility
2. **Case Insensitive**: `DATABASE_TO_LOWER=TRUE` makes identifiers case-insensitive
3. **NULL Ordering**: `DEFAULT_NULL_ORDERING=HIGH` matches PostgreSQL behavior
4. **PostgreSQL Dialect**: Uses `PostgreSQLDialect` for Hibernate compatibility

## Test Configuration Files

### 1. `application-test.yml`
Main test configuration with database and CORS settings:

```yaml
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration

  datasource:
    url: jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
    driverClassName: org.h2.Driver

cors:
  dev:
    allowOrigins: "*"
    allowCredentials: false
    allowMethods: [GET, POST, OPTIONS, DELETE, PUT]
    maxAge: "3600"
    allowHeaders: [Authorization, Content-Type, Accept, Origin, Cache-Control]
```

### 2. `TestDatabaseConfig.java`
Java configuration for test database setup:

```java
@TestConfiguration
@Profile("test")
public class TestDatabaseConfig {
    @Bean
    @Primary
    public DataSource testDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("org.h2.Driver");
        dataSource.setUrl("jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH");
        dataSource.setUsername("sa");
        dataSource.setPassword("");
        return dataSource;
    }
}
```

## Test Classes

### 1. Database Compatibility Tests

**`DatabaseCompatibilityTest.java`** - Tests PostgreSQL compatibility features:

- ✅ Database connection and metadata
- ✅ SERIAL data type (auto-increment)
- ✅ BOOLEAN data type
- ✅ TIMESTAMP with defaults
- ✅ UUID data type
- ✅ Case-insensitive identifiers
- ✅ NULL ordering behavior

### 2. CORS Configuration Tests

**`CorsConfigurationTest.java`** - Unit tests for CORS properties:

- ✅ Dev environment configuration
- ✅ Prod environment configuration
- ✅ Environment selection logic
- ✅ Default values

**`CorsIntegrationTest.java`** - Integration tests for CORS behavior:

- ✅ Preflight requests (OPTIONS)
- ✅ Actual CORS requests
- ✅ Different origins
- ✅ Various HTTP methods
- ✅ Headers and credentials

### 3. Application Context Tests

**`ApplicationContextTest.java`** - Tests application startup:

- ✅ Spring context loads successfully
- ✅ Database beans are configured
- ✅ CORS properties are loaded
- ✅ Security is properly excluded
- ✅ Test profile is active

## Running Tests

### Command Line

```bash
# Run all tests
./mvnw test

# Run specific test class
./mvnw test -Dtest=DatabaseCompatibilityTest

# Run tests with specific profile
./mvnw test -Dspring.profiles.active=test

# Run tests with debug logging
./mvnw test -Dlogging.level.com.smaile.health=DEBUG
```

### IDE Configuration

When running tests in your IDE:

1. **Set Active Profile**: `test`
2. **VM Options**: `-Dspring.profiles.active=test`
3. **Environment Variables**: Not required for basic tests

### Maven Configuration

Tests are automatically run with the `test` profile. The `pom.xml` includes:

```xml
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>test</scope>
</dependency>
```

## Database Features Tested

### PostgreSQL Compatibility

| Feature | Status | Notes |
|---------|--------|-------|
| SERIAL data type | ✅ Supported | Auto-increment primary keys |
| BOOLEAN data type | ✅ Supported | True/false values |
| TIMESTAMP | ✅ Supported | With DEFAULT CURRENT_TIMESTAMP |
| UUID | ✅ Supported | With RANDOM_UUID() function |
| JSON | ⚠️ Limited | Basic support, not full PostgreSQL JSON |
| Case-insensitive identifiers | ✅ Supported | DATABASE_TO_LOWER=TRUE |
| NULL ordering | ✅ Supported | DEFAULT_NULL_ORDERING=HIGH |

### Limitations

1. **JSON Functions**: Advanced PostgreSQL JSON functions may not work
2. **Extensions**: PostgreSQL extensions are not available
3. **Performance**: H2 performance characteristics differ from PostgreSQL
4. **Advanced Types**: Some PostgreSQL-specific types may not be supported

## CORS Testing

### Test Scenarios

1. **Preflight Requests**: OPTIONS method with CORS headers
2. **Simple Requests**: GET/POST with Origin header
3. **Complex Requests**: PUT/DELETE requiring preflight
4. **Credentials**: Testing allowCredentials setting
5. **Headers**: Custom headers in requests
6. **Max Age**: Preflight cache duration

### Expected Behavior

- **Dev Environment**: Wildcard origins (`*`), no credentials
- **Test Environment**: Uses dev configuration by default
- **All Methods**: GET, POST, PUT, DELETE, OPTIONS supported
- **Headers**: Authorization, Content-Type, Accept, Origin, Cache-Control

## Debugging Tests

### H2 Console

Access H2 console during test debugging:
- **URL**: `http://localhost:8000/h2-console`
- **JDBC URL**: `jdbc:h2:mem:testdb`
- **Username**: `sa`
- **Password**: (empty)

### Logging

Enable debug logging in tests:

```yaml
logging:
  level:
    com.smaile.health: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
```

### Common Issues

1. **Database Connection**: Ensure H2 dependency is in test scope
2. **CORS Headers**: Check that test profile is active
3. **Security**: Verify security auto-configuration is excluded
4. **JPA**: Ensure PostgreSQL dialect works with H2 compatibility mode

## Best Practices

1. **Use `@ActiveProfiles("test")`** on all test classes
2. **Clean up test data** in teardown methods if needed
3. **Test database compatibility** for new PostgreSQL features
4. **Verify CORS configuration** for new endpoints
5. **Use integration tests** for end-to-end scenarios
6. **Mock external dependencies** when appropriate

## Continuous Integration

For CI/CD pipelines:

```bash
# Run tests in headless mode
./mvnw test -Djava.awt.headless=true

# Generate test reports
./mvnw test jacoco:report

# Run tests with coverage
./mvnw clean test jacoco:report
```

The test configuration ensures that tests run consistently across different environments without requiring external database setup.
