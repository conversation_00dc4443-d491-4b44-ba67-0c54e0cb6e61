# CORS Configuration Guide

This document provides a comprehensive guide for configuring Cross-Origin Resource Sharing (CORS) in the SMAILE Spring Boot application using dynamic YAML-based configuration.

## Overview

The CORS configuration system provides:
- **Environment-specific settings** (development, staging, production)
- **Dynamic configuration** through environment variables
- **Security best practices** with validation and monitoring
- **Fallback mechanisms** for missing configurations
- **Comprehensive logging** for debugging and monitoring

## Configuration Structure

### Main Configuration File: `application.yml`

```yaml
cors:
  global:
    enabled: ${CORS_ENABLED:true}
    allowed-origins:
      - ${CORS_ALLOWED_ORIGIN_1:http://localhost:3000}
      - ${CORS_ALLOWED_ORIGIN_2:http://localhost:8080}
    allowed-methods:
      - ${CORS_METHOD_GET:GET}
      - ${CORS_METHOD_POST:POST}
      - ${CORS_METHOD_PUT:PUT}
      - ${CORS_METHOD_DELETE:DELETE}
      - ${CORS_METHOD_OPTIONS:OPTIONS}
    allowed-headers:
      - ${CORS_HEADER_AUTHORIZATION:Authorization}
      - ${CORS_HEADER_CONTENT_TYPE:Content-Type}
      - ${CORS_HEADER_ACCEPT:Accept}
    allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}
    max-age: ${CORS_MAX_AGE:3600}
  
  security:
    validate-origins: ${CORS_VALIDATE_ORIGINS:true}
    log-requests: ${CORS_LOG_REQUESTS:false}
    reject-null-origin: ${CORS_REJECT_NULL_ORIGIN:true}
    max-allowed-origins: ${CORS_MAX_ALLOWED_ORIGINS:10}
```

## Environment-Specific Configurations

### Development (`application-dev.yml`)
- **Permissive settings** for local development
- **Multiple localhost origins** for different dev servers
- **Wildcard headers** allowed for easier testing
- **Extended cache time** (24 hours)
- **Logging enabled** for debugging

### Production (`application-prod.yml`)
- **Strict origin validation** with specific domains only
- **Limited HTTP methods** for security
- **Explicit headers** (no wildcards)
- **Shorter cache time** (1 hour)
- **Logging disabled** for performance

## Environment Variables

### Global CORS Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_ENABLED` | `true` | Enable/disable CORS globally |
| `CORS_ALLOWED_ORIGIN_1` | `http://localhost:3000` | Primary allowed origin |
| `CORS_ALLOWED_ORIGIN_2` | `http://localhost:8080` | Secondary allowed origin |
| `CORS_ALLOW_CREDENTIALS` | `true` | Allow credentials in requests |
| `CORS_MAX_AGE` | `3600` | Preflight cache duration (seconds) |

### HTTP Methods
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_METHOD_GET` | `GET` | Allow GET requests |
| `CORS_METHOD_POST` | `POST` | Allow POST requests |
| `CORS_METHOD_PUT` | `PUT` | Allow PUT requests |
| `CORS_METHOD_DELETE` | `DELETE` | Allow DELETE requests |
| `CORS_METHOD_OPTIONS` | `OPTIONS` | Allow OPTIONS requests (required) |

### Headers Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_HEADER_AUTHORIZATION` | `Authorization` | Authorization header |
| `CORS_HEADER_CONTENT_TYPE` | `Content-Type` | Content-Type header |
| `CORS_HEADER_ACCEPT` | `Accept` | Accept header |
| `CORS_HEADER_PRIORITY` | `priority` | Custom priority header |

### Environment-Specific Variables

#### Development
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_DEV_ENABLED` | `true` | Enable CORS for development |
| `CORS_DEV_ORIGIN_1` | `http://localhost:3000` | React dev server |
| `CORS_DEV_ORIGIN_2` | `http://localhost:3001` | Alternative dev port |
| `CORS_DEV_ORIGIN_3` | `http://localhost:8080` | Spring Boot dev |
| `CORS_DEV_MAX_AGE` | `86400` | 24-hour cache for dev |

#### Production
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_PROD_ENABLED` | `true` | Enable CORS for production |
| `CORS_PROD_ORIGIN_1` | `https://app.smaile.com` | Main production app |
| `CORS_PROD_ORIGIN_2` | `https://ui.x.com` | External UI domain |
| `CORS_PROD_MAX_AGE` | `3600` | 1-hour cache for prod |

### Security Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_VALIDATE_ORIGINS` | `true` | Validate origins against whitelist |
| `CORS_LOG_REQUESTS` | `false` | Log CORS requests for monitoring |
| `CORS_REJECT_NULL_ORIGIN` | `true` | Reject requests with null origin |
| `CORS_MAX_ALLOWED_ORIGINS` | `10` | Maximum number of allowed origins |

## Usage Examples

### 1. Local Development Setup
```bash
# Set environment variables for local development
export CORS_DEV_ORIGIN_1=http://localhost:3000
export CORS_DEV_ORIGIN_2=http://localhost:5173  # Vite dev server
export CORS_LOG_REQUESTS=true
export CORS_DEV_MAX_AGE=86400

# Run the application
./mvnw spring-boot:run --spring.profiles.active=dev
```

### 2. Production Deployment
```bash
# Set production environment variables
export CORS_PROD_ORIGIN_1=https://app.smaile.com
export CORS_PROD_ORIGIN_2=https://admin.smaile.com
export CORS_PROD_MAX_AGE=3600
export CORS_VALIDATE_ORIGINS=true

# Deploy with production profile
java -jar app.jar --spring.profiles.active=prod
```

### 3. Docker Environment
```dockerfile
# In Dockerfile or docker-compose.yml
ENV CORS_PROD_ORIGIN_1=https://app.smaile.com
ENV CORS_PROD_ORIGIN_2=https://ui.x.com
ENV CORS_ALLOW_CREDENTIALS=true
ENV CORS_MAX_AGE=3600
```

### 4. Kubernetes ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cors-config
data:
  CORS_PROD_ORIGIN_1: "https://app.smaile.com"
  CORS_PROD_ORIGIN_2: "https://ui.x.com"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
```

## Security Best Practices

### 1. Origin Validation
- ✅ **Use specific domains** instead of wildcards in production
- ✅ **Validate origins** against a whitelist
- ✅ **Reject null origins** for security
- ❌ **Avoid using `*`** for allowed origins in production

### 2. Method Restrictions
- ✅ **Limit HTTP methods** to only what's needed
- ✅ **Always include OPTIONS** for preflight requests
- ❌ **Don't allow unnecessary methods** like TRACE or CONNECT

### 3. Header Management
- ✅ **Specify explicit headers** in production
- ✅ **Include authentication headers** when needed
- ❌ **Avoid wildcard headers** (`*`) in production

### 4. Credentials Handling
- ✅ **Enable credentials** only when necessary
- ✅ **Use HTTPS** when credentials are allowed
- ⚠️ **Never combine** `allowCredentials: true` with `allowedOrigins: ["*"]`

## Monitoring and Debugging

### Enable CORS Logging
```yaml
cors:
  security:
    log-requests: true

logging:
  level:
    com.smaile.health.config.WebSecurityConfig: DEBUG
```

### Common CORS Issues and Solutions

1. **"CORS policy: No 'Access-Control-Allow-Origin' header"**
   - Check if the requesting origin is in `allowed-origins`
   - Verify the origin URL matches exactly (including protocol and port)

2. **"CORS policy: The request client is not a secure context"**
   - Use HTTPS for production origins
   - Ensure `allow-credentials` is properly configured

3. **"CORS policy: Request header field 'authorization' is not allowed"**
   - Add `Authorization` to `allowed-headers`
   - Check for typos in header names

### Health Check Endpoint
The application provides a health check endpoint that includes CORS configuration status:
```
GET /actuator/health/cors
```

## Migration from Hardcoded Configuration

If migrating from hardcoded CORS configuration:

1. **Backup existing configuration**
2. **Add YAML properties** to application files
3. **Update WebSecurityConfig** to use CorsProperties
4. **Test with different environments**
5. **Verify security settings** are maintained

## Troubleshooting

### Configuration Not Loading
- Verify `@ConfigurationProperties` is properly annotated
- Check that `CorsProperties` is being injected
- Ensure YAML syntax is correct

### Environment Variables Not Working
- Verify environment variable names match exactly
- Check that variables are set in the deployment environment
- Use `${VAR_NAME:default_value}` syntax for fallbacks

### CORS Still Blocked
- Check browser developer tools for specific error messages
- Verify the request origin matches configured origins exactly
- Ensure preflight requests (OPTIONS) are handled correctly
