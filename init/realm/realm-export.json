{"id": "efcac130-783e-4e53-9b87-518553d811bd", "realm": "smaile", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "c885288b-3706-4c3e-ae1a-47f74b652f34", "name": "default-roles-smaile", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "efcac130-783e-4e53-9b87-518553d811bd", "attributes": {}}, {"id": "8fe9a660-8b93-426f-af87-7cc7e44a6f21", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "efcac130-783e-4e53-9b87-518553d811bd", "attributes": {}}, {"id": "********-a073-4636-ac60-e5dd266b30df", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "efcac130-783e-4e53-9b87-518553d811bd", "attributes": {}}], "client": {"realm-management": [{"id": "614136e4-1b55-44b2-8f63-3d3dada51a64", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "ccb7fd8b-2088-44c0-a566-a9a094edf530", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "39e93cc3-3166-4bed-8661-ee04c3ff7289", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "8c2ed98a-e3c9-45a6-a60f-12b1f2353de0", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "cbb0addf-8657-4866-b90e-aefb75511c86", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "92a2a20c-bf43-4b34-9364-9a9f9ed1ba8f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "f1bde450-2dfc-47e4-a549-9a82a5915a8a", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "307a7f86-ff12-4329-9096-da81859f984f", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "cf950537-a185-44bc-b8bc-b0988d11bd1e", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-realm", "query-clients", "view-clients", "manage-events", "view-users", "view-identity-providers", "manage-authorization", "query-groups", "view-events", "view-realm", "query-realms", "create-client", "view-authorization", "impersonation", "manage-identity-providers", "manage-users", "manage-clients", "query-users"]}}, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "dbaabfd3-6e4e-4c97-b3c3-309c15074b60", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "696e9a1f-3d6f-40cd-9026-de90c5c4e2ae", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "fce2d490-a7c0-4824-b66f-fe14571c8c24", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "b50e3c4b-442b-4786-9e8e-5c7a394e925d", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "81e4657b-0e87-4868-9e9b-0a8b6ad383f3", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "1a504150-139e-4777-a5eb-c45dad3c5b4d", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "64c1b8c7-bdaf-4563-a047-7f4ed26dab02", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "344b5a11-bc92-401f-9c58-8bc563696ec8", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "e01e438e-f1a2-4e74-b4e9-45c18fd64fff", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}, {"id": "c74ade5e-5664-42ef-923b-7d4571c12be3", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "attributes": {}}], "smaile-be": [], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "76f1ac6e-f1e4-4c8a-8416-8132fa13c697", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "b2413d13-d0ba-4bd0-a76b-d75fdac320d1", "attributes": {}}], "account": [{"id": "4769a7ab-7101-421b-a940-18669943c3f6", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "5a44c189-fd6e-4f20-873e-db444c44087d", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "c093d5ec-3bf2-4496-b933-6a985d03fa1f", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "2e41b7f0-a1b7-432e-9222-666439c4a3c6", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "be28edcd-b8be-4d77-8f8c-e6dbe8e9d7da", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "c3e670dc-b33a-4ea8-827e-5ee4f37c6e64", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "5002ac8f-3c4e-4f8b-9354-133564d24ae3", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}, {"id": "f38b8ff2-4ea6-4dff-a531-9f6da0acd31f", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "ba54b583-ba02-4f6a-a464-21d178b18f80", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "c885288b-3706-4c3e-ae1a-47f74b652f34", "name": "default-roles-smaile", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "efcac130-783e-4e53-9b87-518553d811bd"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "5b2a88f7-bbae-420e-b970-52a06df0ccd6", "createdTimestamp": **********338, "username": "service-account-smaile-be", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "smaile-be", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-smaile"], "notBefore": 0, "groups": []}, {"id": "5b2a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-1", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "5b2a88f7-bbae-420e-b970-52a06df0ccd8", "createdTimestamp": **********338, "username": "smaile-2", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "5b2a88f7-bbae-420e-b970-52a06df0ccd9", "createdTimestamp": **********338, "username": "smaile-3", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "6b2a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-4", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "7b2a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-5", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "8b2a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-6", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "9b2a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-7", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "9b3a88f7-bbae-420e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-8", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "9b3a88f7-bbae-520e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-9", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}, {"id": "9b3a88f7-bbae-530e-b970-52a06df0ccd7", "createdTimestamp": **********338, "username": "smaile-10", "enabled": true, "totp": false, "emailVerified": false, "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": [], "credentials": [{"type": "password", "value": "123456"}]}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "ba54b583-ba02-4f6a-a464-21d178b18f80", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/smaile/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/smaile/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d0d7d2ea-13d5-4470-96db-3ae3262fb5fd", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/smaile/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/smaile/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "066c63f2-c4bc-4d68-8208-c0989d758109", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f2d51a2b-5f9e-4750-9016-bba97de8bb84", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b2413d13-d0ba-4bd0-a76b-d75fdac320d1", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f607de23-fc1d-4c28-bb09-8f03b436a06d", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ff0ed579-2add-46ab-bfcc-b159be7990db", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/smaile/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/smaile/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "8c1d7481-3930-4c99-80ef-0fffbcef669f", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6c7e1044-cf1c-4590-80f9-44dbb00676f3", "clientId": "smaile-be", "name": "smaile-be", "description": "smaile-be", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": true, "clientAuthenticatorType": "client-secret", "secret": "B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "*", "oauth2.device.authorization.grant.enabled": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "a6bf9f33-a3ff-4436-bdff-d018f643b006", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "fdf3591d-7f99-4b02-a948-463cd0147348", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "52f392fd-d676-4df7-989e-5404d0c4ec79", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "c26567e2-2606-49fb-9e36-34e9334b841a", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "0f065ada-8621-473b-932a-0e4160661679", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "a0f074e3-ccf9-4a5a-920b-5c1023d20497", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "07705db9-72b4-46ce-9b95-e4fb6b744b20", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "ce88b142-56d4-4752-873f-b1773c521f8e", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "4f13262e-c3af-4c63-bbc4-95327d2c494c", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "540de725-e34c-471a-ba02-7c75d3c8143b", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "b3fd1a3d-4864-4a7d-b428-a39f287c523e", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "0323d24c-2580-4bcd-8246-56ef9485e783", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "e3f76d09-810f-4b8b-8707-f30a42d5345c", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "a7354599-61d4-414c-9e6d-5a1ee960017e", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "4165f681-7af9-4db9-b816-3eeae8e349d0", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "ac870a99-7385-41a8-8cac-01222943e45f", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "b00b1377-01b0-4798-970a-a78c6d1ee74b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "24c5a4cc-5a62-4a4a-87cd-9afe4c9cd6cf", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "f78ee66a-5ef7-4248-b8bd-ae8ac3bfccf6", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "89460976-a061-44a2-a1b7-7cb7969dfa19", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "a559501a-ec0a-4c9d-9425-3f606ca6057d", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "1e48d1f3-c336-4c84-80c3-0feaf7702bba", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "7f5df5ae-453d-4a1f-864e-3bab58d886b2", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "2291351a-4737-4167-9628-8d9f18892275", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "fa74bdb3-71ee-4cfa-b705-76425741de69", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}]}, {"id": "5f5ae55a-c1d1-4ced-92c5-1de685987924", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "37b7f5a4-2cbc-42db-850e-154b692ad486", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"id": "c16d2d2b-aee1-4d5d-8abb-fa8e679f86ed", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "9ad71193-a9c3-45ac-91ac-a117668404a5", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String"}}]}, {"id": "e6ad814f-7351-43e1-be99-2e45ececabe6", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "ea282391-9e43-4147-b668-9a83c6a291dc", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "21d54098-37e1-42c6-a1ef-a345a1f4b98b", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "68c03af7-d6bd-4163-b2fb-bbe2b88b93fa", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "414efdce-b938-4de0-8bcf-61150fdf85e9", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "61d895e6-5ab5-406e-a841-e0bc7261502c", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "a77eed6f-0e3e-439c-b449-ebc771cec1e6", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "e932c854-6f73-452f-bb49-52df586421f4", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "033ff8b0-8201-4ad1-bf41-16b0e5280a87", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "eaa17a6f-925e-4af5-a64d-0f16bf7c5acd", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "35536c7f-698a-492a-af03-8831bf4e445b", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "ec0d0c54-a8ca-4637-9204-308795be16ca", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "f9c9c2b3-96b9-4205-9ea2-0a491556efbe", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "1f5b99e5-6209-4864-b438-20497b723211", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "efadd007-b589-4c08-b068-317d7a6263aa", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "a760b6dc-3d77-41ee-952d-01b87cf801e5", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper"]}}, {"id": "8539776f-d2d7-48b7-a0a3-54477487168e", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "4673b73f-61d2-4ef8-94c4-1637b2ec89ca", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "598d25c9-3a89-4487-9e3c-401cd85a8522", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "1145eec9-5642-42cd-96e9-d321bfd6096c", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "9fabcba9-97a8-4b05-bcee-f5cf84cc898b", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "7268c276-6f82-4da1-b697-f87c86c8bd75", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "bd9de8ef-b02a-4fe0-a7db-9d3a69e38b73", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "e6d479ef-396b-4183-be6d-914fe49bc5b7", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "d4b005fb-dce8-4ba0-8436-8c5a228113c4", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0cb41b18-c3e9-43d4-b0e9-9acb1a8ccb24", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "dacb5676-3029-4c27-bacd-20ff90777407", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "21f05a9c-5187-41cc-8386-ca2f7f89a8bf", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "607b28d9-5f8b-4a18-990d-43dcbddc81ec", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "4b7b32a3-c7f3-4646-9afc-347b789fdf4b", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "0a4575c1-05e0-4b77-bed2-d4dddce741cd", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "0a30dc0d-9718-4aa8-a8cc-2b279a2ac3ae", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "7267d2d5-e1b9-4a3a-b319-3e5db2ba872a", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3a1d11df-0bf4-42f3-a41c-721ab4d65f84", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "629fbfe3-ec46-4da4-a78f-0f5f52854f74", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "c74f7726-69d0-4cd4-93da-e94d734df146", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "be1bbccb-0f77-42b9-b408-78404e0fbe7e", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "28aac7e9-4fff-45f1-867a-5892aaa70b94", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "4e9ee56f-bb5a-4129-bcae-c329b80dc0ea", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "01c983d5-68cd-4393-97f4-37306ab6460b", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "bbf13149-e40e-400b-b194-b99419c96674", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "f99b1d8a-5820-495c-a7b1-3efc404abdc3", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "fc2c7209-fbcc-467a-a65b-a55e0dee5f53", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "23.0.7", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}