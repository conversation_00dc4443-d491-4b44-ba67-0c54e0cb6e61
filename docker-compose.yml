services:
  iam:
    image: quay.io/keycloak/keycloak:26.3.0
    container_name: iam
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: username
      <PERSON>_DB_PASSWORD: password

      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false

      KC_LOG_LEVEL: info
      KC_METRICS_ENABLED: true
      KC_HEALTH_ENABLED: true
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    command: start-dev --import-realm --hostname iam
    volumes:
      - ./init/realm:/opt/keycloak/data/import
    depends_on:
      - database
    ports:
      - 8080:8080
    networks:
      - iam_network
  oauth2-proxy:
    image: quay.io/oauth2-proxy/oauth2-proxy:latest
    ports:
      - "4180:4180" # Expose OAuth2 Proxy's default port
    environment:
      - OAUTH2_PROXY_PROVIDER=oidc
      - OAUTH2_PROXY_OIDC_ISSUER_URL=http://iam:8080/realms/smaile
      - OAUTH2_PROXY_CLIENT_ID=smaile-be
      - OAUTH2_PROXY_CLIENT_SECRET=B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB
      - OAUTH2_PROXY_COOKIE_SECRET=randomly_generated_cookie_secret
      - OAUTH2_PROXY_UPSTREAMS=http://host.docker.internal:8000
      - OAUTH2_PROXY_REDIRECT_URL=http://localhost:4180/oauth2/callback
      - OAUTH2_PROXY_EMAIL_DOMAINS=*
      - OAUTH2_PROXY_PASS_USER_HEADERS=true
    command:
      - --http-address=0.0.0.0:4180
    networks:
      - iam_network
    depends_on:
      - iam
  database:
    image: postgres:17.5
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: username
      POSTGRES_PASSWORD: password
    ports:
      - 5432:5432
    networks:
      - iam_network
volumes:
  postgres_data:
networks:
  default:
    driver: bridge
  iam_network:
    driver: bridge