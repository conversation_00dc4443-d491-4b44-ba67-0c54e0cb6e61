#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 144336 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=18420, tid=22856
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.10+7) (build 17.0.10+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.10+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dclassworlds.conf=E:/workspaces/eastgate/softwares/apache-maven-3.9.1/bin/m2.conf -Dmaven.home=E:/workspaces/eastgate/softwares/apache-maven-3.9.1 -Dlibrary.jansi.path=E:/workspaces/eastgate/softwares/apache-maven-3.9.1/lib/jansi-native -Dmaven.multiModuleProjectDirectory=E:/workspaces/eastgate/source/smaile/code/SMAILE org.codehaus.plexus.classworlds.launcher.Launcher test -Dtest=CorsResponseHeaderTest

Host: Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug 20 09:17:14 2025 SE Asia Standard Time elapsed time: 3.234059 seconds (0d 0h 0m 3s)

---------------  T H R E A D  ---------------

Current thread (0x000001adc0bc0be0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=22856, stack(0x0000007877600000,0x0000007877700000)]


Current CompileTask:
C2:   3234 4431   !   4       java.io.BufferedReader::readLine (327 bytes)

Stack: [0x0000007877600000,0x0000007877700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67d2c9]
V  [jvm.dll+0x834b1a]
V  [jvm.dll+0x8365de]
V  [jvm.dll+0x836c43]
V  [jvm.dll+0x2477ff]
V  [jvm.dll+0xac514]
V  [jvm.dll+0xacb5c]
V  [jvm.dll+0x566513]
V  [jvm.dll+0x1bcf87]
V  [jvm.dll+0x21a049]
V  [jvm.dll+0x219311]
V  [jvm.dll+0x1a502d]
V  [jvm.dll+0x2290de]
V  [jvm.dll+0x2272ac]
V  [jvm.dll+0x7e9bd7]
V  [jvm.dll+0x7e3fda]
V  [jvm.dll+0x67c1b5]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ae095041e0, length=14, elements={
0x000001ad96a04e10, 0x000001adc0ba4370, 0x000001adc0ba5100, 0x000001adc0bba290,
0x000001adc0bbcd80, 0x000001adc0bbf660, 0x000001adc0bbff30, 0x000001adc0bc0be0,
0x000001adc0bc15a0, 0x000001adc0bd13e0, 0x000001adc3820070, 0x000001adc38277f0,
0x000001adc3968970, 0x000001ae0968f0c0
}

Java Threads: ( => current thread )
  0x000001ad96a04e10 JavaThread "main" [_thread_in_native, id=43428, stack(0x0000007876900000,0x0000007876a00000)]
  0x000001adc0ba4370 JavaThread "Reference Handler" daemon [_thread_blocked, id=40452, stack(0x0000007877000000,0x0000007877100000)]
  0x000001adc0ba5100 JavaThread "Finalizer" daemon [_thread_blocked, id=38428, stack(0x0000007877100000,0x0000007877200000)]
  0x000001adc0bba290 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=28652, stack(0x0000007877200000,0x0000007877300000)]
  0x000001adc0bbcd80 JavaThread "Attach Listener" daemon [_thread_blocked, id=912, stack(0x0000007877300000,0x0000007877400000)]
  0x000001adc0bbf660 JavaThread "Service Thread" daemon [_thread_blocked, id=29424, stack(0x0000007877400000,0x0000007877500000)]
  0x000001adc0bbff30 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=47044, stack(0x0000007877500000,0x0000007877600000)]
=>0x000001adc0bc0be0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=22856, stack(0x0000007877600000,0x0000007877700000)]
  0x000001adc0bc15a0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=17728, stack(0x0000007877700000,0x0000007877800000)]
  0x000001adc0bd13e0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=16596, stack(0x0000007877800000,0x0000007877900000)]
  0x000001adc3820070 JavaThread "Notification Thread" daemon [_thread_blocked, id=17328, stack(0x0000007877900000,0x0000007877a00000)]
  0x000001adc38277f0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=39488, stack(0x0000007877b00000,0x0000007877c00000)]
  0x000001adc3968970 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=14332, stack(0x0000007877c00000,0x0000007877d00000)]
  0x000001ae0968f0c0 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=15364, stack(0x0000007877d00000,0x0000007877e00000)]

Other Threads:
  0x000001adc0b9f850 VMThread "VM Thread" [stack: 0x0000007876f00000,0x0000007877000000] [id=19908]
  0x000001adc3822560 WatcherThread [stack: 0x0000007877a00000,0x0000007877b00000] [id=14584]
  0x000001ad96ab17f0 GCTaskThread "GC Thread#0" [stack: 0x0000007876a00000,0x0000007876b00000] [id=18904]
  0x000001ae09ab2ca0 GCTaskThread "GC Thread#1" [stack: 0x0000007877e00000,0x0000007877f00000] [id=46272]
  0x000001ae09ab2f60 GCTaskThread "GC Thread#2" [stack: 0x0000007877f00000,0x0000007878000000] [id=41208]
  0x000001ae09ab2720 GCTaskThread "GC Thread#3" [stack: 0x0000007878000000,0x0000007878100000] [id=38460]
  0x000001ae09ab1960 GCTaskThread "GC Thread#4" [stack: 0x0000007878100000,0x0000007878200000] [id=48108]
  0x000001ae09ab29e0 GCTaskThread "GC Thread#5" [stack: 0x0000007878200000,0x0000007878300000] [id=17548]
  0x000001ae09ab21a0 GCTaskThread "GC Thread#6" [stack: 0x0000007878300000,0x0000007878400000] [id=15080]
  0x000001ae09ab2460 GCTaskThread "GC Thread#7" [stack: 0x0000007878400000,0x0000007878500000] [id=31032]
  0x000001ae09ab13e0 GCTaskThread "GC Thread#8" [stack: 0x0000007878500000,0x0000007878600000] [id=49028]
  0x000001ae09ab1c20 GCTaskThread "GC Thread#9" [stack: 0x0000007878600000,0x0000007878700000] [id=33148]
  0x000001ad96abe5e0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007876b00000,0x0000007876c00000] [id=34124]
  0x000001adc09f4810 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007876c00000,0x0000007876d00000] [id=14840]
  0x000001adc0ad60f0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000007876d00000,0x0000007876e00000] [id=17108]
  0x000001adc0ad8a40 ConcurrentGCThread "G1 Service" [stack: 0x0000007876e00000,0x0000007876f00000] [id=12484]

Threads with active compile tasks:
C2 CompilerThread0     3271 4431   !   4       java.io.BufferedReader::readLine (327 bytes)
C2 CompilerThread1     3271 4476       4       org.eclipse.aether.util.graph.transformer.ConflictResolver$State::push (457 bytes)
C2 CompilerThread2     3271 4490       4       java.util.IdentityHashMap::remove (102 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000601000000, size: 8176 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001adc4000000-0x000001adc4bb0000-0x000001adc4bb0000), size 12255232, SharedBaseAddress: 0x000001adc4000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001adc5000000-0x000001ae05000000, reserved size: 1073741824
Narrow klass base: 0x000001adc4000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8176M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 138667K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 34 young (139264K), 2 survivors (8192K)
 Metaspace       used 16038K, committed 16256K, reserved 1114112K
  class space    used 1808K, committed 1920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000601000000, 0x000000060136ae00, 0x0000000601400000| 85%| O|  |TAMS 0x0000000601000000, 0x0000000601000000| Untracked 
|   1|0x0000000601400000, 0x0000000601400000, 0x0000000601800000|  0%| F|  |TAMS 0x0000000601400000, 0x0000000601400000| Untracked 
|   2|0x0000000601800000, 0x0000000601800000, 0x0000000601c00000|  0%| F|  |TAMS 0x0000000601800000, 0x0000000601800000| Untracked 
|   3|0x0000000601c00000, 0x0000000601c00000, 0x0000000602000000|  0%| F|  |TAMS 0x0000000601c00000, 0x0000000601c00000| Untracked 
|   4|0x0000000602000000, 0x0000000602000000, 0x0000000602400000|  0%| F|  |TAMS 0x0000000602000000, 0x0000000602000000| Untracked 
|   5|0x0000000602400000, 0x0000000602400000, 0x0000000602800000|  0%| F|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|   6|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|   7|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|   8|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|   9|0x0000000603400000, 0x0000000603400000, 0x0000000603800000|  0%| F|  |TAMS 0x0000000603400000, 0x0000000603400000| Untracked 
|  10|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|  11|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|  12|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|  13|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|  14|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|  15|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|  16|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  17|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  18|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  19|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  20|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  21|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  22|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  23|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  24|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  25|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  26|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  27|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  28|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  29|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  30|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  31|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  32|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  33|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  34|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  35|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  36|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  37|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  38|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  39|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  40|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  41|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  42|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  43|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  44|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  45|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  46|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  47|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  48|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  49|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  50|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  51|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  52|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  53|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  54|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  55|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  56|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  57|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  58|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  59|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  60|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  61|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  62|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  63|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  64|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  65|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  66|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  67|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  68|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  69|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  70|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  71|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  72|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  73|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  74|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  75|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  76|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  77|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  78|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  79|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  80|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  81|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  82|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  83|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  84|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  85|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  86|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  87|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  88|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  89|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  90|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  91|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  92|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  93|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  94|0x0000000618800000, 0x0000000618a00800, 0x0000000618c00000| 50%| E|  |TAMS 0x0000000618800000, 0x0000000618800000| Complete 
|  95|0x0000000618c00000, 0x0000000619000000, 0x0000000619000000|100%| E|CS|TAMS 0x0000000618c00000, 0x0000000618c00000| Complete 
|  96|0x0000000619000000, 0x0000000619400000, 0x0000000619400000|100%| E|CS|TAMS 0x0000000619000000, 0x0000000619000000| Complete 
|  97|0x0000000619400000, 0x0000000619800000, 0x0000000619800000|100%| E|CS|TAMS 0x0000000619400000, 0x0000000619400000| Complete 
|  98|0x0000000619800000, 0x0000000619c00000, 0x0000000619c00000|100%| E|CS|TAMS 0x0000000619800000, 0x0000000619800000| Complete 
|  99|0x0000000619c00000, 0x000000061a000000, 0x000000061a000000|100%| E|CS|TAMS 0x0000000619c00000, 0x0000000619c00000| Complete 
| 100|0x000000061a000000, 0x000000061a400000, 0x000000061a400000|100%| E|CS|TAMS 0x000000061a000000, 0x000000061a000000| Complete 
| 101|0x000000061a400000, 0x000000061a800000, 0x000000061a800000|100%| E|CS|TAMS 0x000000061a400000, 0x000000061a400000| Complete 
| 102|0x000000061a800000, 0x000000061ac00000, 0x000000061ac00000|100%| E|CS|TAMS 0x000000061a800000, 0x000000061a800000| Complete 
| 103|0x000000061ac00000, 0x000000061b000000, 0x000000061b000000|100%| E|CS|TAMS 0x000000061ac00000, 0x000000061ac00000| Complete 
| 104|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%| E|CS|TAMS 0x000000061b000000, 0x000000061b000000| Complete 
| 105|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%| E|CS|TAMS 0x000000061b400000, 0x000000061b400000| Complete 
| 106|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%| E|CS|TAMS 0x000000061b800000, 0x000000061b800000| Complete 
| 107|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%| E|CS|TAMS 0x000000061bc00000, 0x000000061bc00000| Complete 
| 108|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%| E|CS|TAMS 0x000000061c000000, 0x000000061c000000| Complete 
| 109|0x000000061c400000, 0x000000061c800000, 0x000000061c800000|100%| E|CS|TAMS 0x000000061c400000, 0x000000061c400000| Complete 
| 110|0x000000061c800000, 0x000000061cc00000, 0x000000061cc00000|100%| E|CS|TAMS 0x000000061c800000, 0x000000061c800000| Complete 
| 111|0x000000061cc00000, 0x000000061d000000, 0x000000061d000000|100%| S|CS|TAMS 0x000000061cc00000, 0x000000061cc00000| Complete 
| 112|0x000000061d000000, 0x000000061d400000, 0x000000061d400000|100%| S|CS|TAMS 0x000000061d000000, 0x000000061d000000| Complete 
| 113|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%| E|CS|TAMS 0x000000061d400000, 0x000000061d400000| Complete 
| 114|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%| E|CS|TAMS 0x000000061d800000, 0x000000061d800000| Complete 
| 115|0x000000061dc00000, 0x000000061e000000, 0x000000061e000000|100%| E|CS|TAMS 0x000000061dc00000, 0x000000061dc00000| Complete 
| 116|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%| E|CS|TAMS 0x000000061e000000, 0x000000061e000000| Complete 
| 117|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%| E|CS|TAMS 0x000000061e400000, 0x000000061e400000| Complete 
| 118|0x000000061e800000, 0x000000061ec00000, 0x000000061ec00000|100%| E|CS|TAMS 0x000000061e800000, 0x000000061e800000| Complete 
| 119|0x000000061ec00000, 0x000000061f000000, 0x000000061f000000|100%| E|CS|TAMS 0x000000061ec00000, 0x000000061ec00000| Complete 
| 120|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%| E|CS|TAMS 0x000000061f000000, 0x000000061f000000| Complete 
| 121|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%| E|CS|TAMS 0x000000061f400000, 0x000000061f400000| Complete 
| 122|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%| E|CS|TAMS 0x000000061f800000, 0x000000061f800000| Complete 
| 123|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%| E|CS|TAMS 0x000000061fc00000, 0x000000061fc00000| Complete 
| 124|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%| E|CS|TAMS 0x0000000620000000, 0x0000000620000000| Complete 
| 125|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%| E|CS|TAMS 0x0000000620400000, 0x0000000620400000| Complete 
| 126|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%| E|CS|TAMS 0x0000000620800000, 0x0000000620800000| Complete 
| 127|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%| E|CS|TAMS 0x0000000620c00000, 0x0000000620c00000| Complete 

Card table byte_map: [0x000001adaea70000,0x000001adafa70000] _byte_map_base: 0x000001adaba68000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001ad96ab1d20, (CMBitMap*) 0x000001ad96ab1d60
 Prev Bits: [0x000001adb0a70000, 0x000001adb8a30000)
 Next Bits: [0x000001adb8a30000, 0x000001adc09f0000)

Polling page: 0x000001ad94990000

Metaspace:

Usage:
  Non-class:     13.90 MB used.
      Class:      1.77 MB used.
       Both:     15.66 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      14.00 MB ( 22%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      15.88 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  1.47 MB
       Class:  13.97 MB
        Both:  15.44 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 112.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 254.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 664.
num_chunk_merges: 0.
num_chunk_splits: 453.
num_chunks_enlarged: 358.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2572Kb max_used=2572Kb free=117427Kb
 bounds [0x000001ada5ff0000, 0x000001ada6280000, 0x000001adad520000]
CodeHeap 'profiled nmethods': size=120000Kb used=10159Kb max_used=10159Kb free=109841Kb
 bounds [0x000001ad9e520000, 0x000001ad9ef10000, 0x000001ada5a50000]
CodeHeap 'non-nmethods': size=5760Kb used=1181Kb max_used=1254Kb free=4578Kb
 bounds [0x000001ada5a50000, 0x000001ada5cc0000, 0x000001ada5ff0000]
 total_blobs=5072 nmethods=4600 adapters=384
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 3.209 Thread 0x000001adc0bc15a0 nmethod 4562 0x000001ad9eee6d90 code [0x000001ad9eee6f40, 0x000001ad9eee70e8]
Event: 3.209 Thread 0x000001adc0bc15a0 4564       3       org.apache.maven.artifact.DefaultArtifact::getDependencyConflictId (37 bytes)
Event: 3.209 Thread 0x000001adc0bc15a0 nmethod 4564 0x000001ad9eee7190 code [0x000001ad9eee73c0, 0x000001ad9eee7998]
Event: 3.209 Thread 0x000001adc0bc15a0 4565       3       org.apache.maven.artifact.DefaultArtifact::getBaseVersion (27 bytes)
Event: 3.209 Thread 0x000001adc0bc15a0 nmethod 4565 0x000001ad9eee7c10 code [0x000001ad9eee7dc0, 0x000001ad9eee8068]
Event: 3.209 Thread 0x000001adc0bc15a0 4563       3       org.apache.maven.artifact.ArtifactUtils::toSnapshotVersion (77 bytes)
Event: 3.210 Thread 0x000001adc0bc15a0 nmethod 4563 0x000001ad9eee8190 code [0x000001ad9eee84a0, 0x000001ad9eee9298]
Event: 3.210 Thread 0x000001adc0bc15a0 4566       3       org.apache.maven.artifact.DefaultArtifact::hashCode (88 bytes)
Event: 3.211 Thread 0x000001adc0bc15a0 nmethod 4566 0x000001ad9eee9790 code [0x000001ad9eee9980, 0x000001ad9eee9cc8]
Event: 3.211 Thread 0x000001ae0968f0c0 nmethod 4418 0x000001ada6262490 code [0x000001ada6262860, 0x000001ada6266988]
Event: 3.211 Thread 0x000001ae0968f0c0 4477       4       java.util.IdentityHashMap::put (137 bytes)
Event: 3.215 Thread 0x000001adc0bc15a0 4568       3       jdk.internal.org.objectweb.asm.Type::getTypeInternal (199 bytes)
Event: 3.216 Thread 0x000001adc0bc15a0 nmethod 4568 0x000001ad9eee9e90 code [0x000001ad9eeea160, 0x000001ad9eeeb0a8]
Event: 3.220 Thread 0x000001ae0968f0c0 nmethod 4477 0x000001ada6269a90 code [0x000001ada6269c40, 0x000001ada626a348]
Event: 3.220 Thread 0x000001ae0968f0c0 4569       4       java.util.HashMap::keysToArray (81 bytes)
Event: 3.228 Thread 0x000001adc0bc15a0 4570       1       org.apache.maven.model.MailingList::setUnsubscribe (6 bytes)
Event: 3.228 Thread 0x000001adc0bc15a0 nmethod 4570 0x000001ada626a710 code [0x000001ada626a8a0, 0x000001ada626a9b8]
Event: 3.230 Thread 0x000001ae0968f0c0 nmethod 4569 0x000001ada626aa10 code [0x000001ada626aba0, 0x000001ada626b138]
Event: 3.230 Thread 0x000001adc0bc15a0 4571       3       org.apache.maven.model.io.xpp3.MavenXpp3Reader::parseReportPlugin (373 bytes)
Event: 3.230 Thread 0x000001ae0968f0c0 4491       4       java.util.IdentityHashMap::closeDeletion (107 bytes)

GC Heap History (6 events):
Event: 0.688 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 524288K, used 24576K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 5235K, committed 5376K, reserved 1114112K
  class space    used 630K, committed 704K, reserved 1048576K
}
Event: 0.698 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 524288K, used 3684K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 5235K, committed 5376K, reserved 1114112K
  class space    used 630K, committed 704K, reserved 1048576K
}
Event: 1.105 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 524288K, used 28260K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 8983K, committed 9216K, reserved 1114112K
  class space    used 1096K, committed 1216K, reserved 1048576K
}
Event: 1.109 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 524288K, used 5280K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 8983K, committed 9216K, reserved 1114112K
  class space    used 1096K, committed 1216K, reserved 1048576K
}
Event: 2.144 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 524288K, used 62624K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 15 young (61440K), 1 survivors (4096K)
 Metaspace       used 14874K, committed 15040K, reserved 1114112K
  class space    used 1782K, committed 1856K, reserved 1048576K
}
Event: 2.152 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 524288K, used 11691K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 14874K, committed 15040K, reserved 1114112K
  class space    used 1782K, committed 1856K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.008 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
Event: 0.032 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
Event: 0.075 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\net.dll
Event: 0.077 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\nio.dll
Event: 0.081 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
Event: 0.191 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jimage.dll
Event: 0.201 Loaded shared library E:\workspaces\eastgate\softwares\apache-maven-3.9.1\lib\jansi-native\Windows\x86_64\jansi.dll
Event: 0.215 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\verify.dll

Deoptimization events (20 events):
Event: 3.217 Thread 0x000001ad96a04e10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001ada622599c relative=0x0000000000000d7c
Event: 3.217 Thread 0x000001ad96a04e10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001ada622599c method=org.eclipse.aether.graph.Dependency$Exclusions.copy(Ljava/util/Collection;)Ljava/util/Set; @ 1 c2
Event: 3.217 Thread 0x000001ad96a04e10 DEOPT PACKING pc=0x000001ada622599c sp=0x00000078769fd880
Event: 3.217 Thread 0x000001ad96a04e10 DEOPT UNPACKING pc=0x000001ada5aa69a3 sp=0x00000078769fd828 mode 2
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001ada624c370 relative=0x00000000000004d0
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001ada624c370 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 62 c2
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT PACKING pc=0x000001ada624c370 sp=0x00000078769fc940
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT UNPACKING pc=0x000001ada5aa69a3 sp=0x00000078769fc8c8 mode 2
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001ada624c370 relative=0x00000000000004d0
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001ada624c370 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 62 c2
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT PACKING pc=0x000001ada624c370 sp=0x00000078769fc940
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT UNPACKING pc=0x000001ada5aa69a3 sp=0x00000078769fc8c8 mode 2
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001ada624c370 relative=0x00000000000004d0
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001ada624c370 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 62 c2
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT PACKING pc=0x000001ada624c370 sp=0x00000078769fc940
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT UNPACKING pc=0x000001ada5aa69a3 sp=0x00000078769fc8c8 mode 2
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001ada624c370 relative=0x00000000000004d0
Event: 3.220 Thread 0x000001ad96a04e10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001ada624c370 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 62 c2
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT PACKING pc=0x000001ada624c370 sp=0x00000078769fc940
Event: 3.220 Thread 0x000001ad96a04e10 DEOPT UNPACKING pc=0x000001ada5aa69a3 sp=0x00000078769fc8c8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.807 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620ff78c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x0000000620ff78c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.200 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000006208b5f80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006208b5f80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.200 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000006208b9690}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006208b9690) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.204 Thread 0x000001ad96a04e10 Exception <a 'java/lang/ExceptionInInitializerError'{0x00000006208e49f0}> (0x00000006208e49f0) 
thrown [s\src\hotspot\share\oops\instanceKlass.cpp, line 1218]
Event: 1.230 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada60993af to 0x000001ada6099514
Event: 1.231 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000006209f3f38}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, int, java.lang.Object)'> (0x00000006209f3f38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.240 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620a498f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000620a498f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.243 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620a59488}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000620a59488) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.445 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoClassDefFoundError'{0x0000000620228760}: com/google/inject/servlet/ServletModuleTargetVisitor> (0x0000000620228760) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 245]
Event: 1.494 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000006203b1ab0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006203b1ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.511 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x000000061fc2a980}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x000000061fc2a980) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.613 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada605e572 to 0x000001ada605ebcc
Event: 1.613 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada6019472 to 0x000001ada6019aec
Event: 2.077 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x000000061dabf710}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000061dabf710) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.102 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada61f5d6f to 0x000001ada61f5f44
Event: 3.102 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada61b39ef to 0x000001ada61b3bc4
Event: 3.102 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada617270f to 0x000001ada6172789
Event: 3.214 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000619304560}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000619304560) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.215 Thread 0x000001ad96a04e10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000619308e70}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000619308e70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.217 Thread 0x000001ad96a04e10 Implicit null exception at 0x000001ada6224c66 to 0x000001ada6225978

VM Operations (20 events):
Event: 1.584 Executing VM operation: HandshakeAllThreads
Event: 1.584 Executing VM operation: HandshakeAllThreads done
Event: 1.633 Executing VM operation: HandshakeAllThreads
Event: 1.633 Executing VM operation: HandshakeAllThreads done
Event: 1.633 Executing VM operation: HandshakeAllThreads
Event: 1.633 Executing VM operation: HandshakeAllThreads done
Event: 1.694 Executing VM operation: ICBufferFull
Event: 1.694 Executing VM operation: ICBufferFull done
Event: 1.963 Executing VM operation: ICBufferFull
Event: 1.963 Executing VM operation: ICBufferFull done
Event: 2.042 Executing VM operation: HandshakeAllThreads
Event: 2.042 Executing VM operation: HandshakeAllThreads done
Event: 2.144 Executing VM operation: G1CollectForAllocation
Event: 2.152 Executing VM operation: G1CollectForAllocation done
Event: 2.470 Executing VM operation: ICBufferFull
Event: 2.470 Executing VM operation: ICBufferFull done
Event: 2.797 Executing VM operation: ICBufferFull
Event: 2.797 Executing VM operation: ICBufferFull done
Event: 3.213 Executing VM operation: ICBufferFull
Event: 3.214 Executing VM operation: ICBufferFull done

Events (20 events):
Event: 1.646 loading class java/nio/DirectByteBuffer$Deallocator
Event: 1.646 loading class java/nio/DirectByteBuffer$Deallocator done
Event: 1.646 loading class sun/nio/ch/IOStatus
Event: 1.646 loading class sun/nio/ch/IOStatus done
Event: 1.647 loading class java/util/AbstractMap$2
Event: 1.647 loading class java/util/AbstractMap$2 done
Event: 1.647 loading class java/util/AbstractMap$2$1
Event: 1.647 loading class java/util/AbstractMap$2$1 done
Event: 1.693 loading class java/lang/Short$ShortCache
Event: 1.693 loading class java/lang/Short$ShortCache done
Event: 1.693 loading class java/lang/Byte$ByteCache
Event: 1.693 loading class java/lang/Byte$ByteCache done
Event: 1.933 loading class java/util/LinkedList$ListItr
Event: 1.933 loading class java/util/LinkedList$ListItr done
Event: 2.003 loading class java/util/LinkedList$LLSpliterator
Event: 2.003 loading class java/util/LinkedList$LLSpliterator done
Event: 2.003 loading class java/lang/invoke/MethodHandle$1
Event: 2.004 loading class java/lang/invoke/MethodHandle$1 done
Event: 2.503 loading class sun/nio/cs/ISO_8859_1$Decoder
Event: 2.503 loading class sun/nio/cs/ISO_8859_1$Decoder done


Dynamic libraries:
0x00007ff78b580000 - 0x00007ff78b58e000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.exe
0x00007ff8bc670000 - 0x00007ff8bc868000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8ba690000 - 0x00007ff8ba752000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff8ba390000 - 0x00007ff8ba687000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8ba100000 - 0x00007ff8ba200000 	C:\Windows\System32\ucrtbase.dll
0x00007ff88d900000 - 0x00007ff88d917000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jli.dll
0x00007ff8bb490000 - 0x00007ff8bb62d000 	C:\Windows\System32\USER32.dll
0x00007ff8b9ef0000 - 0x00007ff8b9f12000 	C:\Windows\System32\win32u.dll
0x00007ff883690000 - 0x00007ff8836ab000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\VCRUNTIME140.dll
0x00007ff8bc300000 - 0x00007ff8bc32b000 	C:\Windows\System32\GDI32.dll
0x00007ff8b9dd0000 - 0x00007ff8b9ee9000 	C:\Windows\System32\gdi32full.dll
0x00007ff8b9d30000 - 0x00007ff8b9dcd000 	C:\Windows\System32\msvcp_win.dll
0x00007ff89a3d0000 - 0x00007ff89a66a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8bc330000 - 0x00007ff8bc3ce000 	C:\Windows\System32\msvcrt.dll
0x00007ff8badf0000 - 0x00007ff8bae1f000 	C:\Windows\System32\IMM32.DLL
0x00007ff897390000 - 0x00007ff89739c000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\vcruntime140_1.dll
0x00007ff85ee30000 - 0x00007ff85eebd000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\msvcp140.dll
0x00007ff808230000 - 0x00007ff808e92000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server\jvm.dll
0x00007ff8bc570000 - 0x00007ff8bc621000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8baba0000 - 0x00007ff8bac3f000 	C:\Windows\System32\sechost.dll
0x00007ff8bb300000 - 0x00007ff8bb426000 	C:\Windows\System32\RPCRT4.dll
0x00007ff8b9d00000 - 0x00007ff8b9d27000 	C:\Windows\System32\bcrypt.dll
0x00007ff8b9250000 - 0x00007ff8b929b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff8a5690000 - 0x00007ff8a5699000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff8a52a0000 - 0x00007ff8a52c7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff8bb160000 - 0x00007ff8bb1cb000 	C:\Windows\System32\WS2_32.dll
0x00007ff8b5790000 - 0x00007ff8b579a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8b90c0000 - 0x00007ff8b90d2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff8b7b60000 - 0x00007ff8b7b72000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff896e80000 - 0x00007ff896e8a000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jimage.dll
0x00007ff8a3ac0000 - 0x00007ff8a3cc1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff888870000 - 0x00007ff8888a4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff8ba300000 - 0x00007ff8ba382000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff87ceb0000 - 0x00007ff87ced5000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
0x00007ff85ed50000 - 0x00007ff85ee27000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
0x00007ff8bb630000 - 0x00007ff8bbd9e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8b7d60000 - 0x00007ff8b8504000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff8ba840000 - 0x00007ff8bab93000 	C:\Windows\System32\combase.dll
0x00007ff8b9650000 - 0x00007ff8b967b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff8baf50000 - 0x00007ff8bb01d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff8bc3d0000 - 0x00007ff8bc47d000 	C:\Windows\System32\SHCORE.dll
0x00007ff8bc490000 - 0x00007ff8bc4eb000 	C:\Windows\System32\shlwapi.dll
0x00007ff8b9c30000 - 0x00007ff8b9c54000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff883670000 - 0x00007ff883689000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\net.dll
0x00007ff8b3260000 - 0x00007ff8b336a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff8b9430000 - 0x00007ff8b949a000 	C:\Windows\system32\mswsock.dll
0x00007ff883630000 - 0x00007ff883646000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\nio.dll
0x00007ff882d40000 - 0x00007ff882d58000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
0x0000000069ac0000 - 0x0000000069ae4000 	E:\workspaces\eastgate\softwares\apache-maven-3.9.1\lib\jansi-native\Windows\x86_64\jansi.dll
0x00007ff8956c0000 - 0x00007ff8956d0000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.10\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\lib\jansi-native\Windows\x86_64

VM Arguments:
jvm_args: -Dclassworlds.conf=E:/workspaces/eastgate/softwares/apache-maven-3.9.1/bin/m2.conf -Dmaven.home=E:/workspaces/eastgate/softwares/apache-maven-3.9.1 -Dlibrary.jansi.path=E:/workspaces/eastgate/softwares/apache-maven-3.9.1/lib/jansi-native -Dmaven.multiModuleProjectDirectory=E:/workspaces/eastgate/source/smaile/code/SMAILE 
java_command: org.codehaus.plexus.classworlds.launcher.Launcher test -Dtest=CorsResponseHeaderTest
java_class_path (initial): E:/workspaces/eastgate/softwares/apache-maven-3.9.1/boot/plexus-classworlds-2.6.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8573157376                                {product} {ergonomic}
   size_t MaxNewSize                               = 5142216704                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8573157376                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:/Users/<USER>/.jdks/corretto-17.0.10
PATH=C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\PuTTY;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;E:\workspaces\setup\apache-ant-1.10.14\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Git\cmd;C:\Program Files\Amazon\AWSCLIV2;C:\Program Files\WireGuard;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\mise\shims;C:\Program Files\nodejs;C:\Program Files\Docker\Docker\resources\bin;E:\workspaces\myspace\acli-rovo;C:\Program Files\Cloudflare\Cloudflare WARP;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\PowerShell\7;C:\Program Files (x86)\Scalefusion\Scalefusion MDM Agent\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin;C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\PuTTY;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Loc
USERNAME=Admin
TERM=xterm-256color
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 1:14 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xca, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 12 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32701M (2318M free)
TotalPageFile size 57964M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 243M, peak: 243M
current process commit charge ("private bytes"): 679M, peak: 681M

vm_info: OpenJDK 64-Bit Server VM (17.0.10+7-LTS) for windows-amd64 JRE (17.0.10+7-LTS), built on Jan 10 2024 22:11:07 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
