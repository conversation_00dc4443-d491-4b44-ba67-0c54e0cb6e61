{"time":"2025-08-14T08:28:31.575308+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-08-14T08:28:32.301821+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-14T08:28:33.5146806+07:00","level":"INFO","msg":"OK   20250424200609_initial.sql (2.55ms)"}
{"time":"2025-08-14T08:28:33.515681+07:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (1ms)"}
{"time":"2025-08-14T08:28:33.5166815+07:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (1ms)"}
{"time":"2025-08-14T08:28:33.5176816+07:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (1ms)"}
{"time":"2025-08-14T08:28:33.5176816+07:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-14T08:28:33.5186823+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-14T08:28:33.5286965+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-14T08:28:33.5296968+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-14T08:28:33.5376953+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-14T08:28:33.5376953+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-14T08:29:06.9877384+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-14T08:29:06.9877384+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-14T08:29:06.9938829+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-14T08:29:06.9938829+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-14T08:29:10.3593827+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01UHMnD9VN9Td33LD9gMyWxB","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.3556952+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01UHMnD9VN9Td33LD9gMyWxB","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.3556952+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_011QWEQma7UK2JGB3f7mEKE2","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.5834298+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_011QWEQma7UK2JGB3f7mEKE2","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.5844332+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01EJkoQf8QhBUXg2uSSHtsXY","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.5854296+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01EJkoQf8QhBUXg2uSSHtsXY","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.5854296+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01JRozW66B2XDXieRvHRcexa","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:11.5894329+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01JRozW66B2XDXieRvHRcexa","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:14.324164+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01JHGSTMx1poTBRSGkP3WyKt","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.4313921+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01JHGSTMx1poTBRSGkP3WyKt","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.4313921+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_019CYdQg7o9sM3WsgYfBbzqt","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.5799129+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_019CYdQg7o9sM3WsgYfBbzqt","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.5799129+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01WvGMf5SQBjxfJfqqgoDrjP","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.7517274+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01WvGMf5SQBjxfJfqqgoDrjP","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.7527114+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01Cf9nZFPvx6BNgYEvZsdnR8","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:15.8567541+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01Cf9nZFPvx6BNgYEvZsdnR8","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:18.7896825+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01MkX72y7ooEvSaCaZR6gPNz","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:19.8210105+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01MkX72y7ooEvSaCaZR6gPNz","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:19.8213249+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01Cm7e3Teg3LhpDdyYN3Cho7","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:20.4701597+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01Cm7e3Teg3LhpDdyYN3Cho7","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:20.4701827+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01M5WKMkAUjt8oq1rjgBgPPQ","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:20.4712253+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01M5WKMkAUjt8oq1rjgBgPPQ","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:23.5742513+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01HFCS2jvWNwyAbMv1xbHhym","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:32.9715528+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01HFCS2jvWNwyAbMv1xbHhym","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:32.9715528+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01UDNtXfGrnz3cArocfrSrvY","name":"edit","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:29:35.1423391+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01UDNtXfGrnz3cArocfrSrvY","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:09.2246345+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01WEFkJxfpMbNTyjxbyUngHa","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:09.418012+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01WEFkJxfpMbNTyjxbyUngHa","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:11.4209591+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_012GkycND2bmqDMW9GMeUSjP","name":"edit","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:12.499157+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_012GkycND2bmqDMW9GMeUSjP","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:15.3974683+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"toolu_01SaPQSTSZcAEARaTjJj3f7L","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:15.6996086+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Finished tool call","toolCall":{"id":"toolu_01SaPQSTSZcAEARaTjJj3f7L","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-14T08:32:15.7086034+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"echo \"\" >> .gitignore && echo \"### Crush ###\" >> .gitignore && echo \".crush/\" >> .gitignore","err":null}
{"time":"2025-08-14T08:41:29.0961204+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-14T08:41:29.0982791+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-14T08:41:29.8666178+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-14T08:41:30.1383507+07:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-14T08:41:30.1383507+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-14T08:41:30.1453521+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-14T08:41:30.1453521+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-14T08:41:30.1525134+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-14T08:41:30.1525134+07:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
