spring:
  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 5
  jpa:
    properties:
      hibernate:
        format_sql: true
        show_sql: true
        jdbc:
          batch_size: 10
  output.ansi.enabled: ALWAYS
  liquibase:
    contexts: development


server:
  port: ${SERVER_PORT:8000}

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID}
  client-secret: ${IAM_CLIENT_SECRET}

management:
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      environment: development

logging:
  level:
    com.smaile.health: DEBUG
    org.springframework.security: DEBUG
    liquibase: DEBUG
    root: INFO

# Development-specific CORS configuration
cors:
  global:
    enabled: true
    # Development allows more permissive origins for local testing
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:4200
      - http://localhost:8080
      - http://127.0.0.1:3000
      - https://localhost:3000
      - http://localhost:5173  # Vite dev server
      - http://localhost:8081  # Alternative dev port

    # More permissive headers for development
    allowed-headers:
      - "*"  # Allow all headers in development for easier testing

    # Longer cache time for development to reduce preflight requests
    max-age: 86400  # 24 hours

    allow-credentials: true

  # Development-specific security settings
  security:
    validate-origins: false  # Less strict validation in development
    log-requests: true       # Enable logging for debugging
    reject-null-origin: false  # Allow null origin for local file testing
    max-allowed-origins: 20  # Higher limit for development
