server:
  servlet:
    context-path: /api/v1


spring:
  profiles:
    default: dev

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: public
    enabled: true
    contexts: default

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

springdoc:
  packages-to-scan: com.smaile.health.controller
  pathsToMatch: /, /**
  swagger-ui:
    enabled: true
    path: /docs

# CORS Configuration for Cross-Origin Resource Sharing
# This configuration allows dynamic management of CORS settings through environment variables
cors:
  # Global CORS settings that apply to all environments unless overridden
  global:
    # Whether CORS is enabled globally (can be overridden per environment)
    enabled: ${CORS_ENABLED:true}

    # Default allowed origins (can be overridden by environment-specific settings)
    allowed-origins:
      - ${CORS_ALLOWED_ORIGIN_1:http://localhost:3000}
      - ${CORS_ALLOWED_ORIGIN_2:http://localhost:8080}

    # Allowed HTTP methods for CORS requests
    allowed-methods:
      - ${CORS_METHOD_GET:GET}
      - ${CORS_METHOD_POST:POST}
      - ${CORS_METHOD_PUT:PUT}
      - ${CORS_METHOD_DELETE:DELETE}
      - ${CORS_METHOD_OPTIONS:OPTIONS}
      - ${CORS_METHOD_PATCH:PATCH}
      - ${CORS_METHOD_HEAD:HEAD}

    # Allowed headers that can be sent in CORS requests
    allowed-headers:
      - ${CORS_HEADER_AUTHORIZATION:Authorization}
      - ${CORS_HEADER_CONTENT_TYPE:Content-Type}
      - ${CORS_HEADER_ACCEPT:Accept}
      - ${CORS_HEADER_ORIGIN:Origin}
      - ${CORS_HEADER_CACHE_CONTROL:Cache-Control}
      - ${CORS_HEADER_X_REQUESTED_WITH:X-Requested-With}
      - ${CORS_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN:Access-Control-Allow-Origin}
      - ${CORS_HEADER_ACCESS_CONTROL_ALLOW_HEADERS:Access-Control-Allow-Headers}
      - ${CORS_HEADER_PRIORITY:priority}
      - ${CORS_HEADER_X_FORWARDED_FOR:X-Forwarded-For}
      - ${CORS_HEADER_X_FORWARDED_PROTO:X-Forwarded-Proto}

    # Headers that are exposed to the client
    exposed-headers:
      - ${CORS_EXPOSED_HEADER_LOCATION:Location}
      - ${CORS_EXPOSED_HEADER_CONTENT_DISPOSITION:Content-Disposition}
      - ${CORS_EXPOSED_HEADER_X_TOTAL_COUNT:X-Total-Count}
      - ${CORS_EXPOSED_HEADER_X_PAGINATION_LIMIT:X-Pagination-Limit}
      - ${CORS_EXPOSED_HEADER_X_PAGINATION_OFFSET:X-Pagination-Offset}

    # Whether credentials (cookies, authorization headers) are allowed
    allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}

    # Maximum age (in seconds) for preflight request caching
    max-age: ${CORS_MAX_AGE:3600}

    # Path patterns where CORS should be applied
    path-patterns:
      - ${CORS_PATH_PATTERN_1:/**}
      - ${CORS_PATH_PATTERN_2:/api/**}

  # Environment-specific CORS configurations
  environments:
    # Development environment CORS settings
    development:
      enabled: ${CORS_DEV_ENABLED:true}
      allowed-origins:
        - ${CORS_DEV_ORIGIN_1:http://localhost:3000}
        - ${CORS_DEV_ORIGIN_2:http://localhost:3001}
        - ${CORS_DEV_ORIGIN_3:http://localhost:8080}
        - ${CORS_DEV_ORIGIN_4:http://127.0.0.1:3000}
        - ${CORS_DEV_ORIGIN_5:https://localhost:3000}
      allow-credentials: ${CORS_DEV_ALLOW_CREDENTIALS:true}
      max-age: ${CORS_DEV_MAX_AGE:86400}

    # Staging environment CORS settings
    staging:
      enabled: ${CORS_STAGING_ENABLED:true}
      allowed-origins:
        - ${CORS_STAGING_ORIGIN_1:https://staging.smaile.com}
        - ${CORS_STAGING_ORIGIN_2:https://staging-ui.smaile.com}
        - ${CORS_STAGING_ORIGIN_3:https://test.smaile.com}
      allow-credentials: ${CORS_STAGING_ALLOW_CREDENTIALS:true}
      max-age: ${CORS_STAGING_MAX_AGE:7200}

    # Production environment CORS settings
    production:
      enabled: ${CORS_PROD_ENABLED:true}
      allowed-origins:
        - ${CORS_PROD_ORIGIN_1:https://app.smaile.com}
        - ${CORS_PROD_ORIGIN_2:https://ui.x.com}
        - ${CORS_PROD_ORIGIN_3:https://admin.smaile.com}
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allow-credentials: ${CORS_PROD_ALLOW_CREDENTIALS:true}
      max-age: ${CORS_PROD_MAX_AGE:3600}

  # Security settings for CORS
  security:
    # Whether to validate origin against a whitelist
    validate-origins: ${CORS_VALIDATE_ORIGINS:true}

    # Whether to log CORS requests for monitoring
    log-requests: ${CORS_LOG_REQUESTS:false}

    # Whether to reject requests with null origin
    reject-null-origin: ${CORS_REJECT_NULL_ORIGIN:true}

    # Maximum number of allowed origins (security limit)
    max-allowed-origins: ${CORS_MAX_ALLOWED_ORIGINS:10}