server:
  servlet:
    context-path: /api/v1


spring:
  profiles:
    default: dev

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: public
    enabled: true
    contexts: default

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

springdoc:
  packages-to-scan: com.smaile.health.controller
  pathsToMatch: /, /**
  swagger-ui:
    enabled: true
    path: /docs

# CORS Configuration for Cross-Origin Resource Sharing
cors:
  dev:
    allowOrigins: "*"
    allowCredentials: false
    allowMethods:
      - GET
      - POST
      - OPTIONS
      - DELETE
    maxAge: "86400"
    allowHeaders:
      - Authorization
      - Content-Type
      - Accept
      - Origin
      - Cache-Control

  prod:
    allowOrigins:
      - "https://app.smaile.com"
    allowCredentials: true
    allowMethods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    maxAge: "3600"
    allowHeaders:
      - Authorization
      - Content-Type
      - Accept
      - Origin
      - Cache-Control
      - X-Requested-With