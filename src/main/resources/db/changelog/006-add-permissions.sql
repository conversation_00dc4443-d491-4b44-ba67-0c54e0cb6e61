--liquibase formatted sql
--changeset longnh:add-permissions

INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('2322cbc3-9958-43e8-bbeb-4ac9962e3ac9'::uuid, 'CREATE_USER', 'users', '*', 'create', 'Create user', 0, '2025-08-15 10:47:10.390', '2025-08-15 10:47:15.169', NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('1456647e-0185-47f0-8fb2-db0bec948ba5'::uuid, 'READ_USER', 'users', '*', 'read', 'Read user', 0, '2025-08-15 10:47:10.390', '2025-08-15 10:47:15.169', NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('152bffeb-a3c4-4b36-b29a-1d5a807fe988'::uuid, 'UPDATE_USER', 'users', '*', 'update', 'Update user', 0, '2025-08-15 10:47:10.390', '2025-08-15 10:47:15.169', NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('cb8e50e2-391d-445c-994e-ead4896eeff6'::uuid, 'DELETE_USER', 'users', '*', 'delete', 'Delete user', 0, '2025-08-15 10:47:10.390', '2025-08-15 10:47:15.169', NULL, NULL);

INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('c7e10913-18d9-4484-9167-fa0d3b007d52'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, '2322cbc3-9958-43e8-bbeb-4ac9962e3ac9'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('05e35e53-9709-4449-a374-120e31640d15'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, '1456647e-0185-47f0-8fb2-db0bec948ba5'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('267801c2-9cba-44e3-af09-8db9d0e83732'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, '152bffeb-a3c4-4b36-b29a-1d5a807fe988'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('67cebcc4-a84e-482b-8344-238dd842e7ca'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, 'cb8e50e2-391d-445c-994e-ead4896eeff6'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('0437fd32-dca0-45c3-abcd-8464717be91f'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa56'::uuid, '2322cbc3-9958-43e8-bbeb-4ac9962e3ac9'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('002df58b-e7e4-464a-b482-53bbb23641a8'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa56'::uuid, '1456647e-0185-47f0-8fb2-db0bec948ba5'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('fe9e23ba-aed1-44b9-ae5a-35145208c584'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa56'::uuid, '152bffeb-a3c4-4b36-b29a-1d5a807fe988'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('300a8e0b-65ed-48d5-ab8c-8bc87868a121'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa56'::uuid, 'cb8e50e2-391d-445c-994e-ead4896eeff6'::uuid, 0, '2025-08-15 10:50:31.228', '2025-08-15 10:50:31.228', NULL, NULL);

--rollback DELETE FROM public.roles_permissions WHERE id='c7e10913-18d9-4484-9167-fa0d3b007d52'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='05e35e53-9709-4449-a374-120e31640d15'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='267801c2-9cba-44e3-af09-8db9d0e83732'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='67cebcc4-a84e-482b-8344-238dd842e7ca'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='0437fd32-dca0-45c3-abcd-8464717be91f'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='002df58b-e7e4-464a-b482-53bbb23641a8'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='fe9e23ba-aed1-44b9-ae5a-35145208c584'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='300a8e0b-65ed-48d5-ab8c-8bc87868a121'::uuid;
--rollback
--rollback DELETE FROM public.permissions WHERE id='2322cbc3-9958-43e8-bbeb-4ac9962e3ac9'::uuid;
--rollback DELETE FROM public.permissions WHERE id='1456647e-0185-47f0-8fb2-db0bec948ba5'::uuid;
--rollback DELETE FROM public.permissions WHERE id='152bffeb-a3c4-4b36-b29a-1d5a807fe988'::uuid;
--rollback DELETE FROM public.permissions WHERE id='cb8e50e2-391d-445c-994e-ead4896eeff6'::uuid;