--liquibase formatted sql

--changeset toanpham:003-add-insurance-companies-table
-- Create insurance_companies table (extends organizations with JOINED strategy)
-- This table contains only InsuranceCompany specific fields and references organizations table
CREATE TABLE insurance_companies (
    id UUID NOT NULL,
    market VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    legal_id VARCHAR(100) NOT NULL UNIQUE,
    PRIMARY KEY (id),
    FOREIGN KEY (id) REFERENCES organizations (id) ON DELETE CASCADE
);

--rollback DROP TABLE insurance_companies;
