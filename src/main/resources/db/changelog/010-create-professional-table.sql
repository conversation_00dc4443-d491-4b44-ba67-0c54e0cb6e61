--liquibase formatted sql
--changeset maiph:010-create-professional-table

CREATE TABLE professionals (
    id UUID NOT NULL,
    full_professional_name VA<PERSON>HAR(255) NOT NULL,
    primary_license_id VARCHAR(255) NOT NULL,

    country VARCHAR(255),
    primary_practice_market VARCHAR(255),
    market_segment VARCHAR(255),
    professional_specialties jsonb,
    licenses jsonb,

    PRIMARY KEY (id),
    FOREIGN KEY (id) REFERENCES organizations (id) ON DELETE CASCADE
);


CREATE TABLE professional_lobs (
    id UUID NOT NULL,
    professional_id UUID NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(255) NOT NULL,
    document_type VARCHAR(255) NOT NULL,
--    file_data BYTEA,
    file_data TEXT,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    <PERSON><PERSON><PERSON><PERSON>Y (id),
    <PERSON>OREI<PERSON><PERSON> KEY (professional_id) REFERENCES professionals (id) ON DELETE CASCADE
);

CREATE TABLE markets (
    id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY (id)
);
CREATE TABLE markets_segments (
    id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY (id)
);
CREATE TABLE professional_specialties (
    id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY (id)
);
CREATE TABLE countries (
    id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY (id)
);


INSERT INTO public.markets
(id, "name", code, "version", date_created, last_updated, created_by, updated_by)
VALUES
(uuid_in(md5(random()::text || random()::text)::cstring), 'EU','EU', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'NATO','NATO', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'CHINA', 'CN', 0, now(), now(), 'auto', 'auto');

INSERT INTO public.markets_segments
(id, "name", code, "version", date_created, last_updated, created_by, updated_by)
VALUES
(uuid_in(md5(random()::text || random()::text)::cstring), 'USA', 'USA', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'USB', 'USB', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'USC', 'USC', 0, now(), now(), 'auto', 'auto');

INSERT INTO public.professional_specialties
(id, "name", code, "version", date_created, last_updated, created_by, updated_by)
VALUES
(uuid_in(md5(random()::text || random()::text)::cstring), 'Doctor', 'DR', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'Nurse', 'NR', 0, now(), now(), 'auto', 'auto');

INSERT INTO public.countries
(id, "name", code, "version", date_created, last_updated, created_by, updated_by)
VALUES
(uuid_in(md5(random()::text || random()::text)::cstring), 'England', 'ENG', 0, now(), now(), 'auto', 'auto'),
(uuid_in(md5(random()::text || random()::text)::cstring), 'UAE', 'UAE', 0, now(), now(), 'auto', 'auto');

UPDATE public.roles SET organization_type = 'PROFESSIONAL', "scope" = 'ADMIN' WHERE id = '62d490f5-fa15-4688-963d-c4e64271fa06'::uuid