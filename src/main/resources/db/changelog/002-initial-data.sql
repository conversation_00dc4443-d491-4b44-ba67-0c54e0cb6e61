--liquibase formatted sql

--changeset maiph:002-initial-data
INSERT INTO public.organizations
(id, parent_id, "name", "type", registration_number, status, contact_phone, contact_email, address, "version", date_created, last_updated, created_by, updated_by)
VALUES
('00000000-0000-0000-0000-000000000000'::uuid, '00000000-0000-0000-0000-000000000000'::uuid, 'Smaile', 'SUPER_SMAILE', '100000', 'ACTIVE', '00000', 'smaile@gmail', 'hehe', 0, '2025-08-12 00:16:47.814', '2025-08-12 00:16:47.814', 'auto', 'auto');

INSERT INTO public.users
(id, keycloak_id, email, full_name, phone, status, "version", date_created, last_updated, created_by, updated_by)
VALUES
('8682dac9-c55f-4592-afd2-d05dd3a85406'::uuid, '5b2a88f7-bbae-420e-b970-52a06df0ccd7', '<EMAIL>', 'admin', '113', 'ACTIVE', 0, '2025-08-12 00:22:45.182', '2025-08-12 00:22:45.182', 'auto', 'auto');

INSERT INTO public.users_organizations
(id, user_id, organization_id, status, date_created, last_updated, created_by, updated_by)
VALUES
('e2a83439-224f-43ea-bc5d-09686d96e37b'::uuid, '8682dac9-c55f-4592-afd2-d05dd3a85406'::uuid, '00000000-0000-0000-0000-000000000000'::uuid, 'ACTIVE', '2025-08-12 00:24:47.071', '2025-08-12 00:24:47.071', 'auto', 'auto');

INSERT INTO public.roles
(id, "name", code, description, organization_type, "scope", status, "version", date_created, last_updated, created_by, updated_by)
VALUES
('62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, 'Smaile Admin', 'SUPER_SMAILE_ADMIN', 'root user', 'SUPER_SMAILE', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa06'::uuid, 'Professional', 'PROFESSIONAL', 'professional', 'SUPER_SMAILE', 'STAFF', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, 'IC admin', 'IC_ADMIN', 'admin of IC', 'IC', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa36'::uuid, 'IC_TPA admin', 'IC_TPA_ADMIN', 'admin of IC_TPA', 'IC_TPA', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa46'::uuid, 'SMAILE_TPA admin', 'SMAILE_TPA_ADMIN', 'admin of SMAILE_TPA', 'SMAILE_TPA', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa56'::uuid, 'SMAILE_MP admin', 'SMAILE_MP_ADMIN', 'admin of SMAILE_MP', 'SMAILE_MP', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa66'::uuid, 'IC_MP admin', 'IC_MP_ADMIN', 'admin of IC_MP', 'IC_MP', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa76'::uuid, 'SMAILETPA_MP admin', 'SMAILETPA_MP_ADMIN', 'admin of SMAILETPA_MP', 'SMAILETPA_MP', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto'),
('62d490f5-fa15-4688-963d-c4e64271fa86'::uuid, 'ICTPA_MP admin', 'ICTPA_MP_ADMIN', 'admin of ICTPA_MP', 'ICTPA_MP', 'ADMIN', 'ACTIVE', 0, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto');

INSERT INTO public.users_roles
(id, user_organization_id, role_id, status, start_time, end_time, assigned_by, date_created, last_updated, created_by, updated_by)
VALUES
('e2a83439-224f-43ea-bc5d-09686d96e37b'::uuid, 'e2a83439-224f-43ea-bc5d-09686d96e37b'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, 'ACTIVE', '1970-01-01 00:00:00.000', '9999-12-31 23:59:59.000', NULL, '2025-08-12 00:40:42.797', '2025-08-12 00:40:42.797', 'auto', 'auto');
