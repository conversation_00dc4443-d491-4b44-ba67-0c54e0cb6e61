package com.smaile.health.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerException extends RuntimeException {

    public InternalServerException() {
        super();
    }

    public InternalServerException(final String message) {
        super(message);
    }

    public InternalServerException(final String message, final Throwable cause) {
        super(message, cause);
    }
}
