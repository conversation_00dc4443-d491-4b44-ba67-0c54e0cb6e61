package com.smaile.health.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
@Slf4j
public class WebSecurityConfig {

    private final AuthenticationFilter authenticationFilter;
    private final CorsProperties corsProperties;
    private final Environment environment;

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(e -> e.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)))
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .requestMatchers("/about-me").authenticated()
                        .requestMatchers("/admin").hasAnyAuthority("%s_%s".formatted(OrganizationType.SUPER_SMAILE, RoleScope.ADMIN))
                        .anyRequest().authenticated())
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

    /**
     * CORS configuration source bean that reads settings from application.yml.
     * Supports dev and prod environments with simple configuration structure.
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        String[] activeProfiles = environment.getActiveProfiles();
        String currentEnvironment = activeProfiles.length > 0 ? activeProfiles[0] : "dev";

        CorsProperties.EnvironmentConfig envConfig = corsProperties.getConfigForEnvironment(currentEnvironment);

        log.info("Initializing CORS configuration for environment: {}", currentEnvironment);

        CorsConfiguration configuration = new CorsConfiguration();

        setAllowOrigins(envConfig, configuration, currentEnvironment);
        setAllowMethods(envConfig, configuration);
        setAllowHeaders(envConfig, configuration);
        setAllowCredentials(configuration, envConfig);
        setMaxAge(envConfig, configuration);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }

    @SuppressWarnings("unchecked")
    private void setAllowOrigins(CorsProperties.EnvironmentConfig envConfig,
                                 CorsConfiguration configuration,
                                 String currentEnvironment) {
        if (envConfig.getAllowOrigins() != null) {
            if (envConfig.getAllowOrigins() instanceof String && "*".equals(envConfig.getAllowOrigins())) {
                configuration.addAllowedOriginPattern("*");
                log.debug("CORS: Allowing all origins (*)");
            } else if (envConfig.getAllowOrigins() instanceof List) {
                List<String> origins = (List<String>) envConfig.getAllowOrigins();
                configuration.setAllowedOrigins(origins);
                log.debug("CORS: Allowed origins: {}", origins);
            }
        } else {
            List<String> defaultOrigins = getDefaultOriginsForEnvironment(currentEnvironment);
            configuration.setAllowedOrigins(defaultOrigins);
            log.warn("No allowed origins configured. Using defaults: {}", defaultOrigins);
        }
    }

    private void setAllowMethods(CorsProperties.EnvironmentConfig envConfig, CorsConfiguration configuration) {
        if (!CollectionUtils.isEmpty(envConfig.getAllowMethods())) {
            configuration.setAllowedMethods(envConfig.getAllowMethods());
            log.debug("CORS: Allowed methods: {}", envConfig.getAllowMethods());
        } else {
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        }
    }

    private void setAllowHeaders(CorsProperties.EnvironmentConfig envConfig, CorsConfiguration configuration) {
        if (!CollectionUtils.isEmpty(envConfig.getAllowHeaders())) {
            configuration.setAllowedHeaders(envConfig.getAllowHeaders());
            log.debug("CORS: Allowed headers: {}", envConfig.getAllowHeaders());
        } else {
            configuration.setAllowedHeaders(Arrays.asList(
                    "Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"
            ));
        }
    }

    private void setAllowCredentials(CorsConfiguration configuration, CorsProperties.EnvironmentConfig envConfig) {
        configuration.setAllowCredentials(envConfig.isAllowCredentials());
        log.debug("CORS: Allow credentials: {}", envConfig.isAllowCredentials());
    }

    private void setMaxAge(CorsProperties.EnvironmentConfig envConfig, CorsConfiguration configuration) {
        try {
            long maxAge = Long.parseLong(envConfig.getMaxAge());
            configuration.setMaxAge(maxAge);
        } catch (NumberFormatException e) {
            log.warn("Invalid maxAge value: {}. Using default 3600 seconds", envConfig.getMaxAge());
            configuration.setMaxAge(3600L);
        }
    }

    /**
     * Provides secure default origins based on the environment when no configuration is found.
     */
    private List<String> getDefaultOriginsForEnvironment(String environment) {
        return switch (environment.toLowerCase()) {
            case "production", "prod" -> List.of("https://app.smaile.com");
            case "staging", "stage" -> List.of("https://staging.smaile.com");
            default -> List.of("*");
        };
    }

}
