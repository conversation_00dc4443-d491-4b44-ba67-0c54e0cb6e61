package com.smaile.health.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
@Slf4j
public class WebSecurityConfig {

    private final AuthenticationFilter authenticationFilter;
    private final CorsProperties corsProperties;
    private final Environment environment;

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(e -> e.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)))
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .requestMatchers("/about-me").authenticated()
                        .requestMatchers("/admin").hasAnyAuthority("%s_%s".formatted(OrganizationType.SUPER_SMAILE, RoleScope.ADMIN))
                        .anyRequest().authenticated())
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

    /**
     * Dynamic CORS configuration source bean that reads settings from application.yml.
     * This configuration provides:
     * 1. Environment-specific CORS settings (dev, staging, prod)
     * 2. Dynamic configuration through environment variables
     * 3. Security best practices with validation
     * 4. Comprehensive logging and monitoring capabilities
     * 5. Fallback to secure defaults if configuration is missing
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        // Get the current active profile
        String[] activeProfiles = environment.getActiveProfiles();
        String currentEnvironment = activeProfiles.length > 0 ? activeProfiles[0] : "development";

        // Get effective CORS configuration for the current environment
        CorsProperties.EffectiveConfig effectiveConfig = corsProperties.getEffectiveConfig(currentEnvironment);

        // Log CORS configuration if enabled
        if (corsProperties.getSecurity().isLogRequests()) {
            log.info("Initializing CORS configuration for environment: {}", currentEnvironment);
            log.debug("CORS Allowed Origins: {}", effectiveConfig.getAllowedOrigins());
            log.debug("CORS Allowed Methods: {}", effectiveConfig.getAllowedMethods());
            log.debug("CORS Max Age: {} seconds", effectiveConfig.getMaxAge());
        }

        // Create CORS configuration
        CorsConfiguration configuration = new CorsConfiguration();

        // Set allowed origins with validation
        if (!CollectionUtils.isEmpty(effectiveConfig.getAllowedOrigins())) {
            // Validate number of origins against security limit
            if (effectiveConfig.getAllowedOrigins().size() > corsProperties.getSecurity().getMaxAllowedOrigins()) {
                log.warn("Number of allowed origins ({}) exceeds security limit ({}). Consider reducing origins for better security.",
                        effectiveConfig.getAllowedOrigins().size(),
                        corsProperties.getSecurity().getMaxAllowedOrigins());
            }
            configuration.setAllowedOrigins(effectiveConfig.getAllowedOrigins());
        } else {
            // Fallback to secure defaults
            log.warn("No allowed origins configured. Using secure defaults for environment: {}", currentEnvironment);
            configuration.setAllowedOrigins(getDefaultOriginsForEnvironment(currentEnvironment));
        }

        // Set allowed methods
        if (!CollectionUtils.isEmpty(effectiveConfig.getAllowedMethods())) {
            configuration.setAllowedMethods(effectiveConfig.getAllowedMethods());
        } else {
            // Default methods
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        }

        // Set allowed headers
        if (!CollectionUtils.isEmpty(effectiveConfig.getAllowedHeaders())) {
            configuration.setAllowedHeaders(effectiveConfig.getAllowedHeaders());
        } else {
            // Default headers
            configuration.setAllowedHeaders(Arrays.asList(
                    "Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With"
            ));
        }

        // Set exposed headers
        if (!CollectionUtils.isEmpty(effectiveConfig.getExposedHeaders())) {
            configuration.setExposedHeaders(effectiveConfig.getExposedHeaders());
        }

        // Set credentials and max age
        configuration.setAllowCredentials(effectiveConfig.isAllowCredentials());
        configuration.setMaxAge(effectiveConfig.getMaxAge());

        // Apply configuration to specified path patterns
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

        if (!CollectionUtils.isEmpty(effectiveConfig.getPathPatterns())) {
            for (String pathPattern : effectiveConfig.getPathPatterns()) {
                source.registerCorsConfiguration(pathPattern, configuration);
            }
        } else {
            // Default to all paths
            source.registerCorsConfiguration("/**", configuration);
        }

        return source;
    }

    /**
     * Provides secure default origins based on the environment when no configuration is found.
     */
    private List<String> getDefaultOriginsForEnvironment(String environment) {
        return switch (environment.toLowerCase()) {
            case "production", "prod" -> Arrays.asList("https://app.smaile.com");
            case "staging", "stage" -> Arrays.asList("https://staging.smaile.com");
            default -> Arrays.asList("http://localhost:3000", "http://localhost:8080");
        };
    }

}
