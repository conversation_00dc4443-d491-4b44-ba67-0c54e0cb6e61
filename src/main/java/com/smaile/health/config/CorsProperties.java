package com.smaile.health.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * Configuration properties for CORS (Cross-Origin Resource Sharing) settings.
 * This class maps the CORS configuration from application.yml to Java objects
 * for dynamic and environment-specific CORS management.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cors")
public class CorsProperties {

    /**
     * Global CORS settings that apply to all environments unless overridden
     */
    private Global global = new Global();

    /**
     * Environment-specific CORS configurations
     */
    private Map<String, EnvironmentConfig> environments;

    /**
     * Security-related CORS settings
     */
    private Security security = new Security();

    @Data
    public static class Global {
        /**
         * Whether CORS is enabled globally
         */
        private boolean enabled = true;

        /**
         * List of allowed origins for CORS requests
         */
        private List<String> allowedOrigins;

        /**
         * List of allowed HTTP methods
         */
        private List<String> allowedMethods;

        /**
         * List of allowed headers
         */
        private List<String> allowedHeaders;

        /**
         * List of headers exposed to the client
         */
        private List<String> exposedHeaders;

        /**
         * Whether credentials are allowed in CORS requests
         */
        private boolean allowCredentials = true;

        /**
         * Maximum age for preflight request caching (in seconds)
         */
        private long maxAge = 3600;

        /**
         * Path patterns where CORS should be applied
         */
        private List<String> pathPatterns;
    }

    @Data
    public static class EnvironmentConfig {
        /**
         * Whether CORS is enabled for this environment
         */
        private boolean enabled = true;

        /**
         * Environment-specific allowed origins
         */
        private List<String> allowedOrigins;

        /**
         * Environment-specific allowed methods
         */
        private List<String> allowedMethods;

        /**
         * Environment-specific allowed headers
         */
        private List<String> allowedHeaders;

        /**
         * Environment-specific exposed headers
         */
        private List<String> exposedHeaders;

        /**
         * Whether credentials are allowed for this environment
         */
        private boolean allowCredentials = true;

        /**
         * Environment-specific max age for preflight caching
         */
        private long maxAge = 3600;

        /**
         * Environment-specific path patterns
         */
        private List<String> pathPatterns;
    }

    @Data
    public static class Security {
        /**
         * Whether to validate origins against a whitelist
         */
        private boolean validateOrigins = true;

        /**
         * Whether to log CORS requests for monitoring
         */
        private boolean logRequests = false;

        /**
         * Whether to reject requests with null origin
         */
        private boolean rejectNullOrigin = true;

        /**
         * Maximum number of allowed origins (security limit)
         */
        private int maxAllowedOrigins = 10;
    }

    /**
     * Get the effective CORS configuration for the current environment.
     * This method merges global settings with environment-specific overrides.
     *
     * @param environment the current environment (e.g., "development", "production")
     * @return the effective configuration for the environment
     */
    public EffectiveConfig getEffectiveConfig(String environment) {
        EffectiveConfig config = new EffectiveConfig();
        
        // Start with global settings
        config.setEnabled(global.isEnabled());
        config.setAllowedOrigins(global.getAllowedOrigins());
        config.setAllowedMethods(global.getAllowedMethods());
        config.setAllowedHeaders(global.getAllowedHeaders());
        config.setExposedHeaders(global.getExposedHeaders());
        config.setAllowCredentials(global.isAllowCredentials());
        config.setMaxAge(global.getMaxAge());
        config.setPathPatterns(global.getPathPatterns());
        
        // Override with environment-specific settings if they exist
        if (environments != null && environments.containsKey(environment)) {
            EnvironmentConfig envConfig = environments.get(environment);
            
            if (envConfig.getAllowedOrigins() != null) {
                config.setAllowedOrigins(envConfig.getAllowedOrigins());
            }
            if (envConfig.getAllowedMethods() != null) {
                config.setAllowedMethods(envConfig.getAllowedMethods());
            }
            if (envConfig.getAllowedHeaders() != null) {
                config.setAllowedHeaders(envConfig.getAllowedHeaders());
            }
            if (envConfig.getExposedHeaders() != null) {
                config.setExposedHeaders(envConfig.getExposedHeaders());
            }
            if (envConfig.getPathPatterns() != null) {
                config.setPathPatterns(envConfig.getPathPatterns());
            }
            
            config.setEnabled(envConfig.isEnabled());
            config.setAllowCredentials(envConfig.isAllowCredentials());
            config.setMaxAge(envConfig.getMaxAge());
        }
        
        return config;
    }

    @Data
    public static class EffectiveConfig {
        private boolean enabled;
        private List<String> allowedOrigins;
        private List<String> allowedMethods;
        private List<String> allowedHeaders;
        private List<String> exposedHeaders;
        private boolean allowCredentials;
        private long maxAge;
        private List<String> pathPatterns;
    }
}
