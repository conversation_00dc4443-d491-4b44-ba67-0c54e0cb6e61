package com.smaile.health.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Simplified CORS configuration properties.
 * Maps the CORS configuration from application.yml for dev and prod environments.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cors")
public class CorsProperties {

    /**
     * Development environment CORS settings
     */
    private EnvironmentConfig dev = new EnvironmentConfig();

    /**
     * Production environment CORS settings
     */
    private EnvironmentConfig prod = new EnvironmentConfig();

    @Data
    public static class EnvironmentConfig {

        private Object allowOrigins;
        private boolean allowCredentials = true;
        private List<String> allowMethods;
        private String maxAge = "3600";
        private List<String> allowHeaders;

    }

    /**
     * Get the CORS configuration for the specified environment.
     *
     * @param environment the environment ("dev" or "prod")
     * @return the environment configuration
     */
    public EnvironmentConfig getConfigForEnvironment(String environment) {
        return switch (environment.toLowerCase()) {
            case "prod", "production" -> prod;
            default -> dev;
        };
    }

}
