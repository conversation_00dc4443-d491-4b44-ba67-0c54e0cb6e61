package com.smaile.health.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Getter
@Configuration
@Profile("!test")
@Slf4j
public class IamConfig {

    public static final String PASSWORD_GRANT_TYPE = "password";

    @Value("${iam.realm}")
    private String realm;

    @Value("${iam.endpoint}")
    private String endpoint;

    @Value("${iam.client-id}")
    private String clientId;

    @Value("${iam.client-secret}")
    private String clientSecret;
}
