package com.smaile.health.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

public final class ValidationUtils {

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._-]{3,30}$"
    );
    
    private static final Pattern ORGANIZATION_CODE_PATTERN = Pattern.compile(
            "^[A-Z0-9_]{2,10}$"
    );

    private ValidationUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static boolean isValidEmail(String email) {
        return StringUtils.isNotBlank(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    public static boolean isValidUsername(String username) {
        return StringUtils.isNotBlank(username) && USERNAME_PATTERN.matcher(username).matches();
    }

    public static boolean isValidOrganizationCode(String code) {
        return StringUtils.isNotBlank(code) && ORGANIZATION_CODE_PATTERN.matcher(code).matches();
    }

    public static boolean isValidLength(String value, int minLength, int maxLength) {
        return value != null && value.length() >= minLength && value.length() <= maxLength;
    }

    public static boolean isNotNullAndNotEmpty(String value) {
        return StringUtils.isNotBlank(value);
    }

    public static boolean isValidId(Long id) {
        return id != null && id > 0;
    }

    public static String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        return input.trim().replaceAll("[<>\"'&]", "");
    }
}