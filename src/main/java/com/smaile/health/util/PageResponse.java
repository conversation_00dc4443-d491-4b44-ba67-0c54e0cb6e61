package com.smaile.health.util;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.List;

@Getter
@Setter
@Schema(description = "Paginated response wrapper")
public class PageResponse<T> {

    @Schema(
            description = "List of items in the current page",
            example = "[]"
    )
    private List<T> content;

    @Schema(
            description = "Current page number (0-based)",
            example = "0"
    )
    private int page;

    @Schema(
            description = "Number of items per page",
            example = "20"
    )
    private int size;

    @Schema(
            description = "Total number of items across all pages",
            example = "100"
    )
    private long totalElements;

    @Schema(
            description = "Total number of pages",
            example = "5"
    )
    private int totalPages;

    @Schema(
            description = "Whether this is the first page",
            example = "true"
    )
    private boolean first;

    @Schema(
            description = "Whether this is the last page",
            example = "false"
    )
    private boolean last;

    @Schema(
            description = "Whether there is a next page",
            example = "true"
    )
    private boolean hasNext;

    @Schema(
            description = "Whether there is a previous page",
            example = "false"
    )
    private boolean hasPrevious;

    public PageResponse(Page<T> page) {
        this.content = page.getContent();
        this.page = page.getNumber();
        this.size = page.getSize();
        this.totalElements = page.getTotalElements();
        this.totalPages = page.getTotalPages();
        this.first = page.isFirst();
        this.last = page.isLast();
        this.hasNext = page.hasNext();
        this.hasPrevious = page.hasPrevious();
    }

    public static <T> PageResponse<T> of(Page<T> page) {
        return new PageResponse<>(page);
    }
}