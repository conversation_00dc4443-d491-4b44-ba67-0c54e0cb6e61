package com.smaile.health.mapper;

import com.smaile.health.domain.User;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateUserRequest;
import com.smaile.health.model.request.UpdateUserRequest;
import com.smaile.health.model.response.UserResponse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMapper {
    UserDTO toDTO(CreateUserRequest createUserRequest);
    UserDTO toDTO(UpdateUserRequest updateUserRequest);

    UserResponse toResponse(UserDTO userDto);

    UserDTO toDTO(User user);

    User toEntity(UserDTO userDTO);

    void updateEntityFromDTO(UserDTO userDTO, @MappingTarget User user);
}