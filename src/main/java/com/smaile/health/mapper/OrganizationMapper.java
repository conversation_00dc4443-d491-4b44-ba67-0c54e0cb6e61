package com.smaile.health.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrganizationMapper {
//
//    OrganizationDTO toDTO(Organization organization);
//
//    Organization toEntity(OrganizationDTO organizationDTO);
//
//    void updateEntityFromDTO(OrganizationDTO organizationDTO, @MappingTarget Organization organization);
}