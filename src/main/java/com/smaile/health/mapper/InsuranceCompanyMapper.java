package com.smaile.health.mapper;

import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.model.InsuranceCompanyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InsuranceCompanyMapper {

    InsuranceCompanyDTO toDTO(InsuranceCompany insuranceCompany);

    InsuranceCompany toEntity(InsuranceCompanyDTO insuranceCompanyDTO);

    void updateEntityFromDTO(InsuranceCompanyDTO insuranceCompanyDTO, @MappingTarget InsuranceCompany insuranceCompany);
}
