package com.smaile.health.mapper;

import com.smaile.health.model.CreateInsuranceCompanyRequestDTO;
import com.smaile.health.model.InsuranceCompanyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CreateInsuranceCompanyRequestMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    InsuranceCompanyDTO toInsuranceCompanyDTO(CreateInsuranceCompanyRequestDTO requestDTO);
}
