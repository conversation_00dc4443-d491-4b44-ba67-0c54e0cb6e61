package com.smaile.health.constants;

/**
 * Enum to manage all message keys used throughout the SMAILE project.
 * This provides a centralized way to manage internationalization message keys.
 */
public enum MessageKey {

    // ========================================
    // PROFESSIONAL MESSAGES
    // ========================================
    PROFESSIONAL_LICENSE_ID_EXISTED ("msg.professional.duplicate-license-id"),


    // ========================================
    // INSURANCE COMPANY MESSAGES
    // ========================================

    // Success messages
    INSURANCE_COMPANY_CREATED("msg.insurance-company.created"),
    INSURANCE_COMPANY_UPDATED("msg.insurance-company.updated"),
    INSURANCE_COMPANY_RETRIEVED("msg.insurance-company.retrieved"),
    INSURANCE_COMPANY_APPROVED("msg.insurance-company.approved"),
    INSURANCE_COMPANY_ARCHIVED("msg.insurance-company.archived"),

    // Error messages
    INSURANCE_COMPANY_OPERATION_FAILED("msg.insurance-company.operation.failed"),
    INSURANCE_COMPANY_CANNOT_UPDATE_ARCHIVED("msg.insurance-company.cannot-update-archived"),
    INSURANCE_COMPANY_ALREADY_ARCHIVED("msg.insurance-company.already-archived"),
    INSURANCE_COMPANY_ALREADY_APPROVED("msg.insurance-company.already-approved"),

    // ========================================
    // ORGANIZATION MESSAGES
    // ========================================

    // Success messages
    ORGANIZATION_CREATED("msg.organization.created"),
    ORGANIZATION_UPDATED("msg.organization.updated"),
    ORGANIZATION_RETRIEVED("msg.organization.retrieved"),
    ORGANIZATION_DELETED("msg.organization.deleted"),

    // ========================================
    // USER MESSAGES
    // ========================================

    // Success messages
    USER_CREATED("msg.user.created"),
    USER_UPDATED("msg.user.updated"),
    USER_RETRIEVED("msg.user.retrieved"),
    USER_DELETED("msg.user.deleted"),

    // Error messages
    USER_OPERATION_FAILED("msg.user.operation.failed"),
    USER_NOT_FOUND("msg.user.not-found"),
    USER_ALREADY_EXISTS("msg.user.already-exists"),

    // ========================================
    // ROLE MESSAGES
    // ========================================

    // Success messages
    ROLE_CREATED("msg.role.created"),
    ROLE_UPDATED("msg.role.updated"),
    ROLE_RETRIEVED("msg.role.retrieved"),
    ROLE_DELETED("msg.role.deleted"),

    // Error messages
    ROLE_OPERATION_FAILED("msg.role.operation.failed"),
    ROLE_NOT_FOUND("msg.role.not-found"),
    ROLE_ALREADY_EXISTS("msg.role.already-exists"),

    // ========================================
    // AUTHENTICATION & AUTHORIZATION MESSAGES
    // ========================================

    // Success messages
    LOGIN_SUCCESS("msg.auth.login.success"),
    LOGOUT_SUCCESS("msg.auth.logout.success"),
    PASSWORD_CHANGED("msg.auth.password.changed"),

    // Error messages
    LOGIN_FAILED("msg.auth.login.failed"),
    INVALID_CREDENTIALS("msg.auth.invalid-credentials"),
    ACCOUNT_LOCKED("msg.auth.account-locked"),
    TOKEN_EXPIRED("msg.auth.token-expired"),
    INSUFFICIENT_PERMISSIONS("msg.auth.insufficient-permissions"),

    // ========================================
    // VALIDATION MESSAGES
    // ========================================

    // Field validation
    FIELD_REQUIRED("msg.validation.field.required"),
    FIELD_INVALID_FORMAT("msg.validation.field.invalid-format"),
    FIELD_TOO_LONG("msg.validation.field.too-long"),
    FIELD_TOO_SHORT("msg.validation.field.too-short"),

    // Business validation
    DUPLICATE_ENTRY("msg.validation.duplicate-entry"),
    REFERENCED_ENTITY("msg.validation.referenced-entity"),
    INVALID_STATUS_TRANSITION("msg.validation.invalid-status-transition"),

    // ========================================
    // SYSTEM MESSAGES
    // ========================================

    // Success messages
    OPERATION_SUCCESS("msg.system.operation.success"),
    DATA_SAVED("msg.system.data.saved"),
    DATA_UPDATED("msg.system.data.updated"),
    DATA_DELETED("msg.system.data.deleted"),

    // Error messages
    OPERATION_FAILED("msg.system.operation.failed"),
    SYSTEM_ERROR("msg.system.error"),
    SERVICE_UNAVAILABLE("msg.system.service-unavailable"),
    DATABASE_ERROR("msg.system.database-error"),

    // ========================================
    // NOTIFICATION MESSAGES
    // ========================================

    // Success notifications
    NOTIFICATION_SENT("msg.notification.sent"),
    EMAIL_SENT("msg.notification.email.sent"),

    // Error notifications
    NOTIFICATION_FAILED("msg.notification.failed"),
    EMAIL_FAILED("msg.notification.email.failed"),

    // ========================================
    // FILE OPERATION MESSAGES
    // ========================================

    // Success messages
    FILE_UPLOADED("msg.file.uploaded"),
    FILE_DOWNLOADED("msg.file.downloaded"),
    FILE_DELETED("msg.file.deleted"),

    // Error messages
    FILE_UPLOAD_FAILED("msg.file.upload.failed"),
    FILE_NOT_FOUND("msg.file.not-found"),
    FILE_TOO_LARGE("msg.file.too-large"),
    INVALID_FILE_TYPE("msg.file.invalid-type"),

    // ========================================
    // SEARCH & FILTER MESSAGES
    // ========================================

    // Success messages
    SEARCH_COMPLETED("msg.search.completed"),
    FILTERS_APPLIED("msg.search.filters-applied"),

    // Information messages
    NO_RESULTS_FOUND("msg.search.no-results"),
    SEARCH_TIMEOUT("msg.search.timeout"),

    // ========================================
    // PAGINATION MESSAGES
    // ========================================

    // Information messages
    PAGE_INFO("msg.pagination.page-info"),
    TOTAL_ELEMENTS("msg.pagination.total-elements"),

    // ========================================
    // HEALTH CHECK MESSAGES
    // ========================================

    // Success messages
    HEALTH_CHECK_PASSED("msg.health.check.passed"),
    SERVICE_HEALTHY("msg.health.service.healthy"),

    // Error messages
    HEALTH_CHECK_FAILED("msg.health.check.failed"),
    SERVICE_UNHEALTHY("msg.health.service.unhealthy"),

    // ========================================
    // GENERAL MESSAGES
    // ========================================

    // Success messages
    SUCCESS("msg.general.success"),
    WELCOME("msg.general.welcome"),

    // Information messages
    INFO("msg.general.info"),
    WARNING("msg.general.warning"),

    // Error messages
    ERROR("msg.general.error"),
    UNKNOWN_ERROR("msg.general.unknown-error");

    private final String key;

    MessageKey(String key) {
        this.key = key;
    }

    /**
     * Get the message key string value.
     *
     * @return the message key string
     */
    public String getKey() {
        return key;
    }

    /**
     * Get the message key string value (alias for getKey).
     *
     * @return the message key string
     */
    public String getMessageKey() {
        return key;
    }

    @Override
    public String toString() {
        return key;
    }
}
