package com.smaile.health.constants;

import lombok.Getter;

@Getter
public enum RoleEnum {
    SUPER_SMAILE_ADMIN("SUPER_SMAILE_ADMIN", "Smaile system Admin"),
    PROFESSIONAL("PROFESSIONAL", "Professional"),
    IC_ADMIN("IC_ADMIN", "Insurance Company Admin"),
    IC_TPA_ADMIN("IC_TPA_ADMIN", "Insurance Company TPA Admin"),
    SMAILE_TPA_ADMIN("SMAILE_TPA_ADMIN", "Smaile TPA Admin"),
    SMAILE_MP_ADMIN("SMAILE_MP_ADMIN", "Smaile Medical Provider Admin"),
    IC_MP_ADMIN("IC_MP_ADMIN", "Insurance Company Medical Provider Admin"),
    SMAILE_TPA_MP_ADMIN("SMAILE_TPA_MP_ADMIN", "Smaile TPA Medical Provider Admin"),
    IC_TPA_MP_ADMIN("IC_TPA_MP_ADMIN", "Insurance Company TPA Medical Provider Admin");

    private String code;
    private String description;

    RoleEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
