package com.smaile.health.aop;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.RoleEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Set;
import java.util.UUID;

@Component("permissionEvaluator")
@Slf4j
public class CustomPermissionEvaluator implements PermissionEvaluator {
    @Override
    public boolean hasPermission(Authentication auth, Object targetDomainObject, Object permission) {
        CustomAuthentication authentication = (CustomAuthentication) auth;
        // Super admin has authority on all actions at PoC phase
        if (authentication.getRoles().contains(RoleEnum.SUPER_SMAILE_ADMIN.getCode())) return true;

        // All non-super admin require orgId param
        if (targetDomainObject == null) {
            throw new IllegalStateException("Missing parameter for permission check");
        }
        UUID orgId = (UUID) targetDomainObject;


        if (!authentication.getOrganizationToPermissionsMap()
                .getOrDefault(orgId, Set.of())
                .contains(String.valueOf(permission))) {
            log.debug("User {} is missing permission {} on orgId {}", authentication.getActor().getEmail(), permission, orgId);
            return false;
        }
        return true;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return false;
    }
}
