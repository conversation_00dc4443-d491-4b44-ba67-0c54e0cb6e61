package com.smaile.health.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class LogAspect {
    @Around("@annotation(com.smaile.health.aop.LogExecution)")
    public Object logMethodExecutionData(ProceedingJoinPoint jointPoint) throws Throwable {
        String className = jointPoint.getTarget().getClass().getSimpleName();
        String methodName = jointPoint.getSignature().getName();
        log.debug("--> Invoking {}.{} {}", className, methodName, jointPoint.getArgs() != null ? jointPoint.getArgs() : "()");

        Object result = jointPoint.proceed();

        MethodSignature signature = (MethodSignature) jointPoint.getSignature();
        if (signature.getReturnType() == void.class) {
            log.debug("<-- {}.{} finished", className, methodName);
        } else {
            log.debug("<-- {}.{} returns: [{}]", className, methodName, result);
        }
        return result;
    }
}
