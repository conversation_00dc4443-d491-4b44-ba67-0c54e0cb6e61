package com.smaile.health.controller;

import com.smaile.health.model.AboutMeDTO;
import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.service.ProfessionalService;
import com.smaile.health.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

@RestController
@RequestMapping(value = "/register", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
public class RegisterController {

    private final ProfessionalService professionalService;

    @PostMapping(value = "/professional",consumes = {"multipart/form-data", MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<BaseResponse<UUID>> registerProfessional(
            @RequestPart("form") RegisterProfessionalFormDTO registerProfessionalFormDTO,
            @RequestPart(value = "license", required = false) MultipartFile license,
            @RequestPart(value = "certificate", required = false) MultipartFile certificate,
            @RequestPart(value = "liability_insurance", required = false) MultipartFile liabilityInsurance,
            @RequestPart(value = "education_diploma", required = false) MultipartFile educationDiploma,
            @RequestPart(value = "photo", required = false) MultipartFile photo
            ) throws IOException {
        registerProfessionalFormDTO.setProfessionalLicenseFile(license);
        registerProfessionalFormDTO.setProfessionalCertificateFile(certificate);
        registerProfessionalFormDTO.setLiabilityInsuranceFile(liabilityInsurance);
        registerProfessionalFormDTO.setEducationDiplomaFile(educationDiploma);
        registerProfessionalFormDTO.setPhotoFile(photo);
        final UUID createdId = professionalService.create(registerProfessionalFormDTO);
        return ResponseEntity.ok(new BaseResponse<>(createdId));
    }

}
