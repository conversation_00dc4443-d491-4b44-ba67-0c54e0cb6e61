package com.smaile.health.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class RoleDTO {

    private UUID id;

    @NotNull
    @Size(max = 255)
    private String name;

    private String description;

    @NotNull
    private String scope;

    @NotNull
    @Size(max = 100)
    private String status;

}
