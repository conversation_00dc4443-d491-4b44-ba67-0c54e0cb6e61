package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@Builder
public class OrganizationDTO {

    private UUID id;

    @NotNull
    @Size(max = 255)
    private String name;

    @NotNull
    @Size(max = 100)
    private String type;

    @NotNull
    @Size(max = 100)
    private String code;


    private String legalId;

    private OrganizationStatus status;

    private String contactPhone;

    private String contactEmail;

    private String address;

}
