package com.smaile.health.model.response;

import lombok.*;

import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListResponse<T> {

    private int code;
    private String message;
    private List<T> data;

    public ListResponse(List<T> data) {
        this.data = data;
    }

    public static <T> ListResponse<T> of(List<T> data) {
        return new ListResponse<>(0, "success", data);
    }
}
