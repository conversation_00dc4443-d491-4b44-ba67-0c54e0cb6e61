package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.SmaileConstant;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class RegisterProfessionalFormDTO {

    // Step 1
    @JsonProperty("full_professional_name")
    @NotNull
    private String fullProfessionalName;

    @JsonProperty("primary_license_id")
    @NotNull(message = "primary_practice_market is required")
    private String primaryLicenseId;

    @JsonProperty("primary_practice_market")
    @NotNull(message = "primary_practice_market is required")
    private String primaryPracticeMarket;

    @JsonProperty("country")
    @NotNull(message = "Country is required")
    private String country;

    @JsonProperty("market_segment")
    @NotNull(message = "primary_practice_market is required")
    private String marketSegment;

    @JsonProperty("professional_specialties")
    @NotNull(message = "primary_practice_market is required")
    private List<String> professionalSpecialties;

    @JsonProperty("professional_licenses")
    @NotNull(message = "Professional licenses are required")
    private List<ProfessionalLicenseDTO> professionalLicenses;

    // Step 2
    @JsonProperty("full_name")
    @NotNull(message = "Full name is required")
    private String fullName;

    @JsonProperty("email")
    @NotNull(message = "Email is required")
    @Pattern(regexp = SmaileConstant.EMAIL_REGEX, message = "Invalid email format")
    private String email;

    @JsonProperty("phone_number")
    @NotNull(message = "Phone number is required")
    private String phoneNumber;

    @JsonProperty("password")
    @NotNull(message = "Password is required")
    private String password;

    // Step 3: File uploads
    @JsonIgnore
    private MultipartFile professionalLicenseFile;
    @JsonIgnore
    private MultipartFile professionalCertificateFile;
    @JsonIgnore
    private MultipartFile educationDiplomaFile;
    @JsonIgnore
    private MultipartFile liabilityInsuranceFile;
    @JsonIgnore
    private MultipartFile photoFile;

}
