package com.smaile.health.repository;

import com.smaile.health.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserRepository extends JpaRepository<User, UUID> {

    User findOneByKeycloakId(String keycloakId);

    Optional<User> findOneByEmail(String email);

    @Query("""
            SELECT u
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
            WHERE uo.status = :status AND uo.organization.id = COALESCE(:orgId, uo.organization.id)
            """)
    Page<User> findByOrgIdAndStatus(@Param("orgId") UUID orgId, @Param("status") String status, Pageable pageable);

    @Query("""
            SELECT u
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
            WHERE uo.status = :activationStatus
                AND uo.organization.id = COALESCE(:scopedOrgId, uo.organization.id)
                AND u.id IN :userIdList
            """)
    List<User> findByOrgIdAndIdInAndStatus(UUID scopedOrgId, List<UUID> userIdList, String activationStatus);
}
