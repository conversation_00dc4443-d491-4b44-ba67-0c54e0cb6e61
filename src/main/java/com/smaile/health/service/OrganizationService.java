package com.smaile.health.service;

import com.smaile.health.domain.Organization;
import com.smaile.health.model.OrganizationDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface OrganizationService {
    Organization getSmaileOrganization();

    Page<OrganizationDTO> findByParentId(UUID parentId, Pageable pageable);
}