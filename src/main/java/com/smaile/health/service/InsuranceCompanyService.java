package com.smaile.health.service;

import com.smaile.health.model.CreateInsuranceCompanyRequestDTO;
import com.smaile.health.model.InsuranceCompanyDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface InsuranceCompanyService {

    Page<InsuranceCompanyDTO> search(String market, String status, String name, Pageable pageable);

    InsuranceCompanyDTO get(UUID id);

    UUID create(CreateInsuranceCompanyRequestDTO requestDTO);

    void update(UUID id, InsuranceCompanyDTO insuranceCompanyDTO);

    void delete(UUID id);

}
