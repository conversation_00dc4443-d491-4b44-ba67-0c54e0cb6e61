package com.smaile.health.service;

public interface I18nService {

    /**
     * Get message by key using current locale
     * @param messageKey the message key
     * @return the localized message
     */
    String getMessage(String messageKey);

    /**
     * Get message by key with parameters using current locale
     * @param messageKey the message key
     * @param params the parameters to substitute
     * @return the localized message with substituted parameters
     */
    String getMessage(String messageKey, Object... params);

    /**
     * Get message by key with specific locale
     * @param messageKey the message key
     * @param locale the locale to use
     * @return the localized message
     */
    String getMessage(String messageKey, String locale);

    /**
     * Get message by key with parameters and specific locale
     * @param messageKey the message key
     * @param locale the locale to use
     * @param params the parameters to substitute
     * @return the localized message with substituted parameters
     */
    String getMessage(String messageKey, String locale, Object... params);
}
