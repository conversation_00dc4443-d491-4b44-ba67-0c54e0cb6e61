package com.smaile.health.service;

import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.RoleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

public interface ProfessionalService {

    UUID create(RegisterProfessionalFormDTO registerProfessionalFormDTO) throws IOException;

    void approves(RoleDTO roleDTO);


}