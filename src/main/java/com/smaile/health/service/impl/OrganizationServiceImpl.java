package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.domain.Organization;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.service.OrganizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static com.smaile.health.constants.Constants.SMAILE_ADMIN_ORG;

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepository organizationRepository;

    @Override
    public Organization getSmaileOrganization() {
        return organizationRepository.getReferenceById(UUID.fromString(SMAILE_ADMIN_ORG));
    }

    @Override
    @LogExecution
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'organizations:*:read')")
    public Page<OrganizationDTO> findByParentId(UUID parentId, Pageable pageable) {
        return organizationRepository.findByParentId(parentId, pageable).map(
                orgEntity -> OrganizationDTO.builder()
                        .id(orgEntity.getId())
                        .code(orgEntity.getCode())
                        .name(orgEntity.getName())
                        .type(orgEntity.getType().toString())
                        .legalId(orgEntity.getRegistrationNumber())
                        .contactPhone(orgEntity.getContactPhone())
                        .contactEmail(orgEntity.getContactEmail())
                        .address(orgEntity.getAddress())
                        .build()
        );
    }

}
