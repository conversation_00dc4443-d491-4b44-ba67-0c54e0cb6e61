package com.smaile.health.service.impl;

import com.smaile.health.service.I18nService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
@RequiredArgsConstructor
@Slf4j
public class I18nServiceImpl implements I18nService {

    private final MessageSource messageSource;

    @Override
    public String getMessage(String messageKey) {
        return getMessage(messageKey, LocaleContextHolder.getLocale());
    }

    @Override
    public String getMessage(String messageKey, Object... params) {
        return getMessage(messageKey, LocaleContextHolder.getLocale(), params);
    }

    @Override
    public String getMessage(String messageKey, String locale) {
        try {
            // Handle different locale formats
            Locale parsedLocale;
            if (locale.contains("_")) {
                String[] parts = locale.split("_");
                if (parts.length >= 2) {
                    parsedLocale = new Locale(parts[0], parts[1]);
                } else {
                    parsedLocale = new Locale(parts[0]);
                }
            } else {
                parsedLocale = Locale.forLanguageTag(locale);
            }
            return getMessage(messageKey, parsedLocale);
        } catch (Exception e) {
            log.warn("Failed to parse locale '{}', falling back to default locale", locale, e);
            return getMessage(messageKey, Locale.ENGLISH);
        }
    }

    @Override
    public String getMessage(String messageKey, String locale, Object... params) {
        try {
            // Handle different locale formats
            Locale parsedLocale;
            if (locale.contains("_")) {
                String[] parts = locale.split("_");
                if (parts.length >= 2) {
                    parsedLocale = new Locale(parts[0], parts[1]);
                } else {
                    parsedLocale = new Locale(parts[0]);
                }
            } else {
                parsedLocale = Locale.forLanguageTag(locale);
            }
            return getMessage(messageKey, parsedLocale, params);
        } catch (Exception e) {
            log.warn("Failed to parse locale '{}', falling back to default locale", locale, e);
            return getMessage(messageKey, Locale.ENGLISH, params);
        }
    }

    private String getMessage(String messageKey, Locale locale) {
        try {
            return messageSource.getMessage(messageKey, null, locale);
        } catch (Exception e) {
            log.error("Failed to get message for key '{}' with locale '{}'", messageKey, locale, e);
            // Return a fallback message
            return "Message not found: " + messageKey;
        }
    }

    private String getMessage(String messageKey, Locale locale, Object... params) {
        try {
            return messageSource.getMessage(messageKey, params, locale);
        } catch (Exception e) {
            log.error("Failed to get message for key '{}' with locale '{}' and params", messageKey, locale, e);
            // Return a fallback message
            return "Message not found: " + messageKey;
        }
    }
}

