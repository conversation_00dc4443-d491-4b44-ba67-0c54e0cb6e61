package com.smaile.health.service.impl;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.ProfessionalDocumentType;
import com.smaile.health.domain.Professional;
import com.smaile.health.domain.ProfessionalLob;
import com.smaile.health.domain.User;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.RoleDTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.*;
import com.smaile.health.service.*;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class ProfessionalServiceImpl implements ProfessionalService {

    private final ProfessionalRepository professionalRepository;
    private final ProfessionalLobRepository professionalLobRepository;
    private final OrganizationService organizationService;

    private final UserService userService;
    private final I18nService i18nService;

    @Override
    @Transactional
    public UUID create(RegisterProfessionalFormDTO registerProfessionalForm) throws IOException {
        // Validate if Professional with same primary license id exists
        if (professionalRepository.existsByPrimaryLicenseId(registerProfessionalForm.getPrimaryLicenseId())) {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_LICENSE_ID_EXISTED.getKey(), registerProfessionalForm.getPrimaryLicenseId())
            );
        }

        Professional professional = createProfessional(registerProfessionalForm);
        professionalRepository.save(professional);
        List<ProfessionalLob> professionalLobs = createProfessionalLob(registerProfessionalForm, professional);
        professionalLobRepository.saveAll(professionalLobs);
        professional.setProfessionalLobs(professionalLobs);
        // Create KC user and User as Admin in Professional Org
        UUID userId = userService.createProfessionalUser(professional.getId(),
                UserDTO.builder()
                        .fullName(registerProfessionalForm.getFullName())
                        .email(registerProfessionalForm.getEmail())
                        .build(),
                registerProfessionalForm.getPassword());
        log.info("Create User id {} in Professional Org {} ", userId, professional.getId());
        return professional.getId();
    }

    @Override
    public void approves(RoleDTO roleDTO) {
        // TODO
    }

    public Professional createProfessional(RegisterProfessionalFormDTO registerForm) {
        Professional professional = new Professional();
        professional.setId(UUIDv7.generate());
        professional.setParent(organizationService.getSmaileOrganization());

        // Professional Info
        professional.setCountry(registerForm.getCountry());
        professional.setFullProfessionalName(registerForm.getFullProfessionalName());
        professional.setProfessionalSpecialties(registerForm.getProfessionalSpecialties());
        professional.setLicenses(registerForm.getProfessionalLicenses());
        professional.setPrimaryLicenseId(registerForm.getPrimaryLicenseId());
        professional.setPrimaryPracticeMarket(registerForm.getPrimaryPracticeMarket());
        professional.setMarketSegment(registerForm.getMarketSegment());

        // Base Org info
        professional.setContactEmail(registerForm.getEmail());
        professional.setName(registerForm.getFullProfessionalName());
        professional.setContactPhone(registerForm.getPhoneNumber());
        professional.setRegistrationNumber(registerForm.getPrimaryLicenseId());

        professional.setStatus(OrganizationStatus.DRAFT);
        professional.setType(OrganizationType.PROFESSIONAL);
        return professional;
    }

    public List<ProfessionalLob> createProfessionalLob(RegisterProfessionalFormDTO registerProfessionalForm, Professional professional) throws IOException {
        List<ProfessionalLob> lobs = new ArrayList<>();
        if (registerProfessionalForm.getProfessionalLicenseFile() != null) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalLicenseFile(), ProfessionalDocumentType.LICENSE));
        }
        if (registerProfessionalForm.getEducationDiplomaFile() != null) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getEducationDiplomaFile(), ProfessionalDocumentType.EDUCATION_DIPLOMA));
        }
        if (registerProfessionalForm.getProfessionalCertificateFile() != null) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalCertificateFile(), ProfessionalDocumentType.CERTIFICATE));
        }
        if (registerProfessionalForm.getLiabilityInsuranceFile() != null) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getLiabilityInsuranceFile(), ProfessionalDocumentType.LIABILITY_INSURANCE));
        }
        if (registerProfessionalForm.getPhotoFile() != null) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getPhotoFile(), ProfessionalDocumentType.PHOTO));
        }
        lobs.forEach(professionalLob -> professionalLob.setProfessional(professional));
        return lobs;
    }

    private ProfessionalLob createProfessionalLob(MultipartFile file, ProfessionalDocumentType documentType) throws IOException {
        ProfessionalLob lob = new ProfessionalLob();
        lob.setId(UUIDv7.generate());
        lob.setFileType(file.getContentType());
        lob.setFileName(file.getName());
        lob.setFileData(Base64.getEncoder().encodeToString(file.getBytes()));
        lob.setDocumentType(documentType);
        return lob;
    }
}
