# Override the properties for test environment here

server:
  port: 8000

spring:
  autoconfigure:
    exclude: # exclude: the security configuration, oauth2 configuration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration

  datasource:
    url: ******************************************
    driverClassName: org.hsqldb.jdbc.JDBCDriver

  jpa:
    database-platform: org.hibernate.dialect.HSQLDialect
    hibernate:
      ddl-auto: create-drop
    defer-datasource-initialization: true
