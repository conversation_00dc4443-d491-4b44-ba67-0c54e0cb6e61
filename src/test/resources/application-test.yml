# Override the properties for test environment here

server:
  port: 8000

spring:
  autoconfigure:
    exclude: # exclude: the security configuration, oauth2 configuration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration

  datasource:
    # H2 in-memory database with PostgreSQL compatibility mode
    url: jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
    driverClassName: org.h2.Driver
    username: sa
    password:
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 5

  jpa:
    # Use PostgreSQL dialect for better compatibility
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create-drop
      # Enable SQL logging for tests
      show_sql: true
      format_sql: true
    defer-datasource-initialization: true
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  # H2 Console for debugging (only in test environment)
  h2:
    console:
      enabled: true
      path: /h2-console

# CORS Configuration for test environment
cors:
  dev:
    allowOrigins: "*"
    allowCredentials: false
    allowMethods:
      - GET
      - POST
      - OPTIONS
      - DELETE
      - PUT
    maxAge: "3600"
    allowHeaders:
      - Authorization
      - Content-Type
      - Accept
      - Origin
      - Cache-Control

# Logging configuration for tests
logging:
  level:
    com.smaile.health: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    root: INFO
