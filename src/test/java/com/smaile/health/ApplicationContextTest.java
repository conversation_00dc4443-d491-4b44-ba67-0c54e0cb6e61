package com.smaile.health;

import com.smaile.health.config.CorsProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simplified test to verify that the Spring application context loads correctly
 * with the test configuration for CORS without requiring database setup.
 */
@SpringBootTest(classes = {CorsProperties.class})
@EnableConfigurationProperties(CorsProperties.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
        "cors.dev.allowOrigins=*",
        "cors.dev.allowCredentials=false",
        "cors.dev.maxAge=3600",
        "cors.prod.allowOrigins[0]=https://app.smaile.com",
        "cors.prod.allowCredentials=true",
        "cors.prod.maxAge=3600"
})
class ApplicationContextTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private CorsProperties corsProperties;

    @Test
    void contextLoads() {
        assertThat(applicationContext).isNotNull();
    }

    @Test
    void testCorsPropertiesAreLoaded() {
        assertThat(corsProperties).isNotNull();
        assertThat(corsProperties.getDev()).isNotNull();
        assertThat(corsProperties.getProd()).isNotNull();

        // Verify dev configuration
        CorsProperties.EnvironmentConfig devConfig = corsProperties.getDev();
        assertThat(devConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(devConfig.isAllowCredentials()).isFalse();
        assertThat(devConfig.getMaxAge()).isEqualTo("3600");
    }

    @Test
    void testEnvironmentProfileIsTest() {
        String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
        assertThat(activeProfiles).contains("test");
    }

    @Test
    void testCorsConfigurationForTestEnvironment() {
        CorsProperties.EnvironmentConfig testConfig = corsProperties.getConfigForEnvironment("test");

        // Should default to dev configuration
        assertThat(testConfig).isEqualTo(corsProperties.getDev());
        assertThat(testConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(testConfig.isAllowCredentials()).isFalse();
    }
}
