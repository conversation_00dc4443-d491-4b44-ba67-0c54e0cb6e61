package com.smaile.health;

import com.smaile.health.config.CorsProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.cors.CorsConfigurationSource;

import javax.sql.DataSource;
import java.sql.Connection;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test to verify that the Spring application context loads correctly
 * with the test configuration including H2 PostgreSQL-compatible database
 * and CORS configuration.
 */
@SpringBootTest
@ActiveProfiles("test")
class ApplicationContextTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private CorsProperties corsProperties;

    @Autowired
    private CorsConfigurationSource corsConfigurationSource;

    @Test
    void contextLoads() {
        // Verify that the Spring application context loads successfully
        assertThat(applicationContext).isNotNull();
    }

    @Test
    void testDatabaseBeanIsConfigured() throws Exception {
        // Verify that the DataSource bean is properly configured
        assertThat(dataSource).isNotNull();
        
        // Verify we can get a connection
        try (Connection connection = dataSource.getConnection()) {
            assertThat(connection).isNotNull();
            assertThat(connection.getMetaData().getURL()).contains("h2:mem:testdb");
            assertThat(connection.getMetaData().getURL()).contains("MODE=PostgreSQL");
        }
    }

    @Test
    void testCorsPropertiesAreLoaded() {
        // Verify that CORS properties are properly loaded
        assertThat(corsProperties).isNotNull();
        assertThat(corsProperties.getDev()).isNotNull();
        assertThat(corsProperties.getProd()).isNotNull();
        
        // Verify dev configuration
        CorsProperties.EnvironmentConfig devConfig = corsProperties.getDev();
        assertThat(devConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(devConfig.isAllowCredentials()).isFalse();
        assertThat(devConfig.getMaxAge()).isEqualTo("3600");
    }

    @Test
    void testCorsConfigurationSourceIsConfigured() {
        // Verify that CORS configuration source bean is created
        assertThat(corsConfigurationSource).isNotNull();
    }

    @Test
    void testTestDatabaseConfigIsActive() {
        // Verify that TestDatabaseConfig is loaded
        assertThat(applicationContext.containsBean("testDataSource")).isTrue();
        
        // Verify the test datasource is the primary one
        DataSource primaryDataSource = applicationContext.getBean("dataSource", DataSource.class);
        assertThat(primaryDataSource).isNotNull();
    }

    @Test
    void testSecurityConfigurationIsExcluded() {
        // Verify that security auto-configuration is excluded in test profile
        // This should not throw an exception since security is disabled
        assertThat(applicationContext).isNotNull();
        
        // The test should pass without authentication requirements
        // since SecurityAutoConfiguration is excluded
    }

    @Test
    void testH2ConsoleIsEnabled() {
        // Verify that H2 console is enabled for debugging in test environment
        // This is configured in application-test.yml
        assertThat(applicationContext).isNotNull();
        
        // H2 console should be available at /h2-console in test environment
        // This is useful for debugging test data
    }

    @Test
    void testLoggingConfigurationIsActive() {
        // Verify that logging configuration is properly set for tests
        // Debug logging should be enabled for com.smaile.health package
        assertThat(applicationContext).isNotNull();
        
        // This test ensures that the logging configuration in application-test.yml
        // is properly applied, which helps with debugging test failures
    }

    @Test
    void testJpaConfigurationWithH2() {
        // Verify that JPA is configured correctly with H2 PostgreSQL mode
        assertThat(applicationContext).isNotNull();
        
        // The application should start without JPA/Hibernate errors
        // even though we're using H2 with PostgreSQL dialect
    }

    @Test
    void testEnvironmentProfileIsTest() {
        // Verify that the test profile is active
        String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
        assertThat(activeProfiles).contains("test");
    }

    @Test
    void testCorsConfigurationForTestEnvironment() {
        // Verify that CORS configuration is appropriate for test environment
        CorsProperties.EnvironmentConfig testConfig = corsProperties.getConfigForEnvironment("test");
        
        // Should default to dev configuration
        assertThat(testConfig).isEqualTo(corsProperties.getDev());
        assertThat(testConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(testConfig.isAllowCredentials()).isFalse();
    }
}
