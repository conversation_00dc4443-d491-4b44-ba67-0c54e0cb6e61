package com.smaile.health.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class for simplified CORS configuration properties.
 */
@SpringBootTest
@TestPropertySource(properties = {
        "cors.dev.allowOrigins=*",
        "cors.dev.allowCredentials=false",
        "cors.dev.allowMethods[0]=GET",
        "cors.dev.allowMethods[1]=POST",
        "cors.dev.maxAge=86400",
        "cors.prod.allowOrigins[0]=https://app.smaile.com",
        "cors.prod.allowCredentials=true",
        "cors.prod.maxAge=3600"
})
class CorsConfigurationTest {

    @Test
    void testDevEnvironmentConfiguration() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.EnvironmentConfig devConfig = new CorsProperties.EnvironmentConfig();
        devConfig.setAllowOrigins("*");
        devConfig.setAllowCredentials(false);
        devConfig.setAllowMethods(Arrays.asList("GET", "POST", "OPTIONS", "DELETE"));
        devConfig.setMaxAge("86400");
        devConfig.setAllowHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"));
        corsProperties.setDev(devConfig);

        // When
        CorsProperties.EnvironmentConfig result = corsProperties.getConfigForEnvironment("dev");

        // Then
        assertThat(result.getAllowOrigins()).isEqualTo("*");
        assertThat(result.isAllowCredentials()).isFalse();
        assertThat(result.getAllowMethods()).containsExactly("GET", "POST", "OPTIONS", "DELETE");
        assertThat(result.getMaxAge()).isEqualTo("86400");
        assertThat(result.getAllowHeaders()).containsExactly("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control");
    }

    @Test
    void testProdEnvironmentConfiguration() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.EnvironmentConfig prodConfig = new CorsProperties.EnvironmentConfig();
        prodConfig.setAllowOrigins(Arrays.asList("https://app.smaile.com", "https://ui.x.com"));
        prodConfig.setAllowCredentials(true);
        prodConfig.setAllowMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        prodConfig.setMaxAge("3600");
        prodConfig.setAllowHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With"));
        corsProperties.setProd(prodConfig);

        // When
        CorsProperties.EnvironmentConfig result = corsProperties.getConfigForEnvironment("prod");

        // Then
        @SuppressWarnings("unchecked")
        java.util.List<String> origins = (java.util.List<String>) result.getAllowOrigins();
        assertThat(origins).containsExactly("https://app.smaile.com", "https://ui.x.com");
        assertThat(result.isAllowCredentials()).isTrue();
        assertThat(result.getAllowMethods()).containsExactly("GET", "POST", "PUT", "DELETE", "OPTIONS");
        assertThat(result.getMaxAge()).isEqualTo("3600");
        assertThat(result.getAllowHeaders()).containsExactly("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With");
    }

    @Test
    void testEnvironmentSelection() {
        // Given
        CorsProperties corsProperties = new CorsProperties();

        // Set up dev config
        CorsProperties.EnvironmentConfig devConfig = new CorsProperties.EnvironmentConfig();
        devConfig.setAllowOrigins("*");
        devConfig.setAllowCredentials(false);
        corsProperties.setDev(devConfig);

        // Set up prod config
        CorsProperties.EnvironmentConfig prodConfig = new CorsProperties.EnvironmentConfig();
        prodConfig.setAllowOrigins(Arrays.asList("https://app.smaile.com"));
        prodConfig.setAllowCredentials(true);
        corsProperties.setProd(prodConfig);

        // When & Then
        assertThat(corsProperties.getConfigForEnvironment("dev")).isEqualTo(devConfig);
        assertThat(corsProperties.getConfigForEnvironment("development")).isEqualTo(devConfig);
        assertThat(corsProperties.getConfigForEnvironment("prod")).isEqualTo(prodConfig);
        assertThat(corsProperties.getConfigForEnvironment("production")).isEqualTo(prodConfig);
        assertThat(corsProperties.getConfigForEnvironment("unknown")).isEqualTo(devConfig); // Falls back to dev
    }

    @Test
    void testDefaultValues() {
        // Given
        CorsProperties corsProperties = new CorsProperties();

        // When & Then (test default values)
        assertThat(corsProperties.getDev().isAllowCredentials()).isTrue();
        assertThat(corsProperties.getDev().getMaxAge()).isEqualTo("3600");
        assertThat(corsProperties.getProd().isAllowCredentials()).isTrue();
        assertThat(corsProperties.getProd().getMaxAge()).isEqualTo("3600");
    }
}
