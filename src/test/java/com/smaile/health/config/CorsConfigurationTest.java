package com.smaile.health.config;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit test class for CORS configuration properties.
 * Tests the configuration logic without requiring Spring context.
 */
class CorsConfigurationTest {

    @Test
    void testDevEnvironmentConfiguration() {
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.EnvironmentConfig devConfig = new CorsProperties.EnvironmentConfig();
        devConfig.setAllowOrigins("*");
        devConfig.setAllowCredentials(false);
        devConfig.setAllowMethods(Arrays.asList("GET", "POST", "OPTIONS", "DELETE"));
        devConfig.setMaxAge("86400");
        devConfig.setAllowHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"));
        corsProperties.setDev(devConfig);

        CorsProperties.EnvironmentConfig result = corsProperties.getConfigForEnvironment("dev");

        assertThat(result.getAllowOrigins()).isEqualTo("*");
        assertThat(result.isAllowCredentials()).isFalse();
        assertThat(result.getAllowMethods()).containsExactly("GET", "POST", "OPTIONS", "DELETE");
        assertThat(result.getMaxAge()).isEqualTo("86400");
        assertThat(result.getAllowHeaders()).containsExactly("Authorization", "Content-Type", "Accept", "Origin",
                "Cache-Control");
    }

    @Test
    void testProdEnvironmentConfiguration() {
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.EnvironmentConfig prodConfig = new CorsProperties.EnvironmentConfig();
        prodConfig.setAllowOrigins(List.of("https://app.smaile.com"));
        prodConfig.setAllowCredentials(true);
        prodConfig.setAllowMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        prodConfig.setMaxAge("3600");
        prodConfig.setAllowHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control",
                "X-Requested-With"));
        corsProperties.setProd(prodConfig);

        CorsProperties.EnvironmentConfig result = corsProperties.getConfigForEnvironment("prod");

        @SuppressWarnings("unchecked")
        List<String> origins = (List<String>) result.getAllowOrigins();
        assertThat(origins).containsExactly("https://app.smaile.com");
        assertThat(result.isAllowCredentials()).isTrue();
        assertThat(result.getAllowMethods()).containsExactly("GET", "POST", "PUT", "DELETE", "OPTIONS");
        assertThat(result.getMaxAge()).isEqualTo("3600");
        assertThat(result.getAllowHeaders()).containsExactly("Authorization", "Content-Type", "Accept", "Origin",
                "Cache-Control", "X-Requested-With");
    }

    @Test
    void testEnvironmentSelection() {
        CorsProperties corsProperties = new CorsProperties();

        CorsProperties.EnvironmentConfig devConfig = new CorsProperties.EnvironmentConfig();
        devConfig.setAllowOrigins("*");
        devConfig.setAllowCredentials(false);
        corsProperties.setDev(devConfig);

        CorsProperties.EnvironmentConfig prodConfig = new CorsProperties.EnvironmentConfig();
        prodConfig.setAllowOrigins(List.of("https://app.smaile.com"));
        prodConfig.setAllowCredentials(true);
        corsProperties.setProd(prodConfig);

        assertThat(corsProperties.getConfigForEnvironment("dev")).isEqualTo(devConfig);
        assertThat(corsProperties.getConfigForEnvironment("development")).isEqualTo(devConfig);
        assertThat(corsProperties.getConfigForEnvironment("prod")).isEqualTo(prodConfig);
        assertThat(corsProperties.getConfigForEnvironment("production")).isEqualTo(prodConfig);
        assertThat(corsProperties.getConfigForEnvironment("unknown")).isEqualTo(devConfig);
    }

    @Test
    void testDefaultValues() {
        CorsProperties corsProperties = new CorsProperties();

        assertThat(corsProperties.getDev().isAllowCredentials()).isTrue();
        assertThat(corsProperties.getDev().getMaxAge()).isEqualTo("3600");
        assertThat(corsProperties.getProd().isAllowCredentials()).isTrue();
        assertThat(corsProperties.getProd().getMaxAge()).isEqualTo("3600");
    }

}
