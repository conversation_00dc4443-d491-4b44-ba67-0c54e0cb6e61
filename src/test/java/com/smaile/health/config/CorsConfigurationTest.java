package com.smaile.health.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class for CORS configuration properties and effective configuration resolution.
 */
@SpringBootTest
@TestPropertySource(properties = {
        "cors.global.enabled=true",
        "cors.global.allowed-origins[0]=http://localhost:3000",
        "cors.global.allowed-origins[1]=http://localhost:8080",
        "cors.global.allowed-methods[0]=GET",
        "cors.global.allowed-methods[1]=POST",
        "cors.global.allow-credentials=true",
        "cors.global.max-age=3600",
        "cors.security.validate-origins=true",
        "cors.security.log-requests=false"
})
class CorsConfigurationTest {

    @Test
    void testCorsPropertiesGlobalConfiguration() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.Global global = new CorsProperties.Global();
        global.setEnabled(true);
        global.setAllowedOrigins(Arrays.asList("http://localhost:3000", "http://localhost:8080"));
        global.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        global.setAllowCredentials(true);
        global.setMaxAge(3600L);
        corsProperties.setGlobal(global);

        // When
        CorsProperties.EffectiveConfig effectiveConfig = corsProperties.getEffectiveConfig("development");

        // Then
        assertThat(effectiveConfig.isEnabled()).isTrue();
        assertThat(effectiveConfig.getAllowedOrigins()).containsExactly("http://localhost:3000", "http://localhost:8080");
        assertThat(effectiveConfig.getAllowedMethods()).containsExactly("GET", "POST", "PUT", "DELETE", "OPTIONS");
        assertThat(effectiveConfig.isAllowCredentials()).isTrue();
        assertThat(effectiveConfig.getMaxAge()).isEqualTo(3600L);
    }

    @Test
    void testEnvironmentSpecificOverrides() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        
        // Set global configuration
        CorsProperties.Global global = new CorsProperties.Global();
        global.setEnabled(true);
        global.setAllowedOrigins(Arrays.asList("http://localhost:3000"));
        global.setAllowCredentials(true);
        global.setMaxAge(3600L);
        corsProperties.setGlobal(global);

        // Set environment-specific configuration
        Map<String, CorsProperties.EnvironmentConfig> environments = new HashMap<>();
        CorsProperties.EnvironmentConfig prodConfig = new CorsProperties.EnvironmentConfig();
        prodConfig.setEnabled(true);
        prodConfig.setAllowedOrigins(Arrays.asList("https://app.smaile.com", "https://ui.x.com"));
        prodConfig.setAllowCredentials(true);
        prodConfig.setMaxAge(1800L);
        environments.put("production", prodConfig);
        corsProperties.setEnvironments(environments);

        // When
        CorsProperties.EffectiveConfig effectiveConfig = corsProperties.getEffectiveConfig("production");

        // Then
        assertThat(effectiveConfig.isEnabled()).isTrue();
        assertThat(effectiveConfig.getAllowedOrigins()).containsExactly("https://app.smaile.com", "https://ui.x.com");
        assertThat(effectiveConfig.isAllowCredentials()).isTrue();
        assertThat(effectiveConfig.getMaxAge()).isEqualTo(1800L);
    }

    @Test
    void testFallbackToGlobalWhenEnvironmentNotFound() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.Global global = new CorsProperties.Global();
        global.setEnabled(true);
        global.setAllowedOrigins(Arrays.asList("http://localhost:3000"));
        global.setAllowCredentials(true);
        global.setMaxAge(3600L);
        corsProperties.setGlobal(global);

        // When (requesting non-existent environment)
        CorsProperties.EffectiveConfig effectiveConfig = corsProperties.getEffectiveConfig("nonexistent");

        // Then (should fall back to global configuration)
        assertThat(effectiveConfig.isEnabled()).isTrue();
        assertThat(effectiveConfig.getAllowedOrigins()).containsExactly("http://localhost:3000");
        assertThat(effectiveConfig.isAllowCredentials()).isTrue();
        assertThat(effectiveConfig.getMaxAge()).isEqualTo(3600L);
    }

    @Test
    void testPartialEnvironmentOverride() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        
        // Set global configuration
        CorsProperties.Global global = new CorsProperties.Global();
        global.setEnabled(true);
        global.setAllowedOrigins(Arrays.asList("http://localhost:3000"));
        global.setAllowedMethods(Arrays.asList("GET", "POST"));
        global.setAllowCredentials(true);
        global.setMaxAge(3600L);
        corsProperties.setGlobal(global);

        // Set partial environment-specific configuration (only origins)
        Map<String, CorsProperties.EnvironmentConfig> environments = new HashMap<>();
        CorsProperties.EnvironmentConfig devConfig = new CorsProperties.EnvironmentConfig();
        devConfig.setAllowedOrigins(Arrays.asList("http://localhost:3000", "http://localhost:3001"));
        // Note: not setting other properties, should inherit from global
        environments.put("development", devConfig);
        corsProperties.setEnvironments(environments);

        // When
        CorsProperties.EffectiveConfig effectiveConfig = corsProperties.getEffectiveConfig("development");

        // Then
        assertThat(effectiveConfig.getAllowedOrigins()).containsExactly("http://localhost:3000", "http://localhost:3001");
        assertThat(effectiveConfig.getAllowedMethods()).containsExactly("GET", "POST"); // From global
        assertThat(effectiveConfig.isAllowCredentials()).isTrue(); // From global
        assertThat(effectiveConfig.getMaxAge()).isEqualTo(3600L); // From global
    }

    @Test
    void testSecurityConfiguration() {
        // Given
        CorsProperties corsProperties = new CorsProperties();
        CorsProperties.Security security = new CorsProperties.Security();
        security.setValidateOrigins(true);
        security.setLogRequests(false);
        security.setRejectNullOrigin(true);
        security.setMaxAllowedOrigins(5);
        corsProperties.setSecurity(security);

        // When & Then
        assertThat(corsProperties.getSecurity().isValidateOrigins()).isTrue();
        assertThat(corsProperties.getSecurity().isLogRequests()).isFalse();
        assertThat(corsProperties.getSecurity().isRejectNullOrigin()).isTrue();
        assertThat(corsProperties.getSecurity().getMaxAllowedOrigins()).isEqualTo(5);
    }

    @Test
    void testDefaultValues() {
        // Given
        CorsProperties corsProperties = new CorsProperties();

        // When & Then (test default values)
        assertThat(corsProperties.getGlobal().isEnabled()).isTrue();
        assertThat(corsProperties.getGlobal().isAllowCredentials()).isTrue();
        assertThat(corsProperties.getGlobal().getMaxAge()).isEqualTo(3600L);
        
        assertThat(corsProperties.getSecurity().isValidateOrigins()).isTrue();
        assertThat(corsProperties.getSecurity().isLogRequests()).isFalse();
        assertThat(corsProperties.getSecurity().isRejectNullOrigin()).isTrue();
        assertThat(corsProperties.getSecurity().getMaxAllowedOrigins()).isEqualTo(10);
    }
}
