package com.smaile.health.config;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpMethod;
import org.springframework.web.cors.CorsConfiguration;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class to verify that CORS configuration produces the correct response headers.
 * Tests the CORS configuration logic without requiring MockMvc or full Spring context.
 */
class CorsResponseHeaderTest {

    @Test
    void testDevEnvironmentCorsConfiguration() {
        // Test CORS configuration for development environment
        CorsConfiguration config = createDevCorsConfiguration();

        // Verify allowed origins
        assertThat(config.getAllowedOriginPatterns()).contains("*");

        // Verify allowed methods
        assertThat(config.getAllowedMethods()).containsExactlyInAnyOrder(
                "GET", "POST", "PUT", "DELETE", "OPTIONS"
        );

        // Verify allowed headers
        assertThat(config.getAllowedHeaders()).containsExactlyInAnyOrder(
                "Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"
        );

        // Verify credentials setting
        assertThat(config.getAllowCredentials()).isFalse();

        // Verify max age
        assertThat(config.getMaxAge()).isEqualTo(3600L);
    }

    @Test
    void testProdEnvironmentCorsConfiguration() {
        // Test CORS configuration for production environment
        CorsConfiguration config = createProdCorsConfiguration();

        // Verify specific allowed origins (no wildcards)
        assertThat(config.getAllowedOrigins()).containsExactlyInAnyOrder(
                "https://app.smaile.com", "https://ui.x.com"
        );

        // Verify allowed methods
        assertThat(config.getAllowedMethods()).containsExactlyInAnyOrder(
                "GET", "POST", "PUT", "DELETE", "OPTIONS"
        );

        // Verify allowed headers
        assertThat(config.getAllowedHeaders()).containsExactlyInAnyOrder(
                "Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With"
        );

        // Verify credentials setting
        assertThat(config.getAllowCredentials()).isTrue();

        // Verify max age
        assertThat(config.getMaxAge()).isEqualTo(3600L);
    }

    @Test
    void testCorsConfigurationHeadersForWildcardOrigin() {
        // Test that wildcard origin configuration accepts any origin
        CorsConfiguration config = createDevCorsConfiguration();

        // With wildcard pattern, checkOrigin returns the actual origin (not "*")
        assertThat(config.checkOrigin("http://localhost:3000")).isEqualTo("http://localhost:3000");
        assertThat(config.checkOrigin("https://example.com")).isEqualTo("https://example.com");
        assertThat(config.checkOrigin("http://any-domain.com")).isEqualTo("http://any-domain.com");

        // Verify that the configuration has wildcard pattern
        assertThat(config.getAllowedOriginPatterns()).contains("*");
    }

    @Test
    void testCorsConfigurationHeadersForSpecificOrigins() {
        // Test that specific origin configuration produces correct headers
        CorsConfiguration config = createProdCorsConfiguration();

        // Simulate checking allowed origins
        assertThat(config.checkOrigin("https://app.smaile.com")).isEqualTo("https://app.smaile.com");
        assertThat(config.checkOrigin("https://ui.x.com")).isEqualTo("https://ui.x.com");
        assertThat(config.checkOrigin("https://unauthorized.com")).isNull();
    }

    @Test
    void testCorsConfigurationMethodsValidation() {
        // Test that method validation works correctly
        CorsConfiguration config = createDevCorsConfiguration();

        // Test allowed methods
        assertThat(config.checkHttpMethod(HttpMethod.GET)).isNotNull();
        assertThat(config.checkHttpMethod(HttpMethod.POST)).isNotNull();
        assertThat(config.checkHttpMethod(HttpMethod.PUT)).isNotNull();
        assertThat(config.checkHttpMethod(HttpMethod.DELETE)).isNotNull();
        assertThat(config.checkHttpMethod(HttpMethod.OPTIONS)).isNotNull();

        // Test disallowed method
        assertThat(config.checkHttpMethod(HttpMethod.TRACE)).isNull();
    }

    @Test
    void testCorsConfigurationHeadersValidation() {
        // Test that header validation works correctly
        CorsConfiguration config = createDevCorsConfiguration();

        // Test allowed headers
        assertThat(config.checkHeaders(Arrays.asList("Authorization"))).isNotNull();
        assertThat(config.checkHeaders(Arrays.asList("Content-Type"))).isNotNull();
        assertThat(config.checkHeaders(Arrays.asList("Accept"))).isNotNull();
        assertThat(config.checkHeaders(Arrays.asList("Origin"))).isNotNull();
        assertThat(config.checkHeaders(Arrays.asList("Cache-Control"))).isNotNull();

        // Test multiple headers
        assertThat(config.checkHeaders(Arrays.asList("Authorization", "Content-Type"))).isNotNull();
    }

    @Test
    void testCorsCredentialsConfiguration() {
        // Test credentials configuration for different environments
        CorsConfiguration devConfig = createDevCorsConfiguration();
        CorsConfiguration prodConfig = createProdCorsConfiguration();

        // Dev should have credentials disabled
        assertThat(devConfig.getAllowCredentials()).isFalse();

        // Prod should have credentials enabled
        assertThat(prodConfig.getAllowCredentials()).isTrue();
    }

    @Test
    void testCorsMaxAgeConfiguration() {
        // Test max age configuration
        CorsConfiguration devConfig = createDevCorsConfiguration();
        CorsConfiguration prodConfig = createProdCorsConfiguration();

        // Both should have same max age for this test
        assertThat(devConfig.getMaxAge()).isEqualTo(3600L);
        assertThat(prodConfig.getMaxAge()).isEqualTo(3600L);
    }

    @Test
    void testCorsResponseHeadersForDevEnvironment() {
        // Test that dev configuration produces expected response headers
        CorsConfiguration config = createDevCorsConfiguration();

        // Verify that the configuration would produce the correct headers
        assertThat(config.getAllowedOriginPatterns()).contains("*");
        assertThat(config.getAllowCredentials()).isFalse();
        assertThat(config.getMaxAge()).isEqualTo(3600L);

        // Verify methods that would be in Access-Control-Allow-Methods header
        assertThat(config.getAllowedMethods()).contains("GET", "POST", "PUT", "DELETE", "OPTIONS");

        // Verify headers that would be in Access-Control-Allow-Headers header
        assertThat(config.getAllowedHeaders()).contains("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control");
    }

    @Test
    void testCorsResponseHeadersForProdEnvironment() {
        // Test that prod configuration produces expected response headers
        CorsConfiguration config = createProdCorsConfiguration();

        // Verify that the configuration would produce the correct headers
        assertThat(config.getAllowedOrigins()).contains("https://app.smaile.com", "https://ui.x.com");
        assertThat(config.getAllowCredentials()).isTrue();
        assertThat(config.getMaxAge()).isEqualTo(3600L);

        // Verify methods that would be in Access-Control-Allow-Methods header
        assertThat(config.getAllowedMethods()).contains("GET", "POST", "PUT", "DELETE", "OPTIONS");

        // Verify headers that would be in Access-Control-Allow-Headers header
        assertThat(config.getAllowedHeaders()).contains("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With");
    }

    /**
     * Helper method to create development CORS configuration
     */
    private CorsConfiguration createDevCorsConfiguration() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        config.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"));
        config.setAllowCredentials(false);
        config.setMaxAge(3600L);
        return config;
    }

    /**
     * Helper method to create production CORS configuration
     */
    private CorsConfiguration createProdCorsConfiguration() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowedOrigins(Arrays.asList("https://app.smaile.com", "https://ui.x.com"));
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        config.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control", "X-Requested-With"));
        config.setAllowCredentials(true);
        config.setMaxAge(3600L);
        return config;
    }
}
