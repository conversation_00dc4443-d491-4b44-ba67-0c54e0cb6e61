package com.smaile.health.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * CORS integration test that focuses on testing CORS configuration
 * properties without requiring WebSecurityConfig or complex dependencies.
 */
@SpringBootTest(classes = { CorsProperties.class })
@EnableConfigurationProperties(CorsProperties.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
        "cors.dev.allowOrigins=*",
        "cors.dev.allowCredentials=false",
        "cors.dev.allowMethods[0]=GET",
        "cors.dev.allowMethods[1]=POST",
        "cors.dev.allowMethods[2]=OPTIONS",
        "cors.dev.allowMethods[3]=DELETE",
        "cors.dev.maxAge=3600",
        "cors.dev.allowHeaders[0]=Authorization",
        "cors.dev.allowHeaders[1]=Content-Type",
        "cors.dev.allowHeaders[2]=Accept",
        "cors.dev.allowHeaders[3]=Origin",
        "cors.dev.allowHeaders[4]=Cache-Control",
        "cors.prod.allowOrigins[0]=https://app.smaile.com",
        "cors.prod.allowCredentials=true",
        "cors.prod.maxAge=3600"
})
class CorsIntegrationTest {

    @Autowired
    private Environment environment;

    @Autowired
    private CorsProperties corsProperties;

    @Test
    void testCorsPropertiesLoaded() {
        assertThat(corsProperties).isNotNull();
    }

    @Test
    void testEnvironmentIsTest() {
        String[] activeProfiles = environment.getActiveProfiles();
        assertThat(activeProfiles).contains("test");
    }

    @Test
    void testCorsConfigurationForTestEnvironment() {
        assertThat(corsProperties).isNotNull();

        CorsProperties.EnvironmentConfig devConfig = corsProperties.getDev();
        assertThat(devConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(devConfig.isAllowCredentials()).isFalse();
        assertThat(devConfig.getMaxAge()).isEqualTo("3600");
    }

    @Test
    void testCorsPropertiesConfiguration() {
        CorsProperties.EnvironmentConfig testConfig = corsProperties.getConfigForEnvironment("test");

        assertThat(testConfig).isEqualTo(corsProperties.getDev());
        assertThat(testConfig.getAllowOrigins()).isEqualTo("*");
        assertThat(testConfig.isAllowCredentials()).isFalse();
    }

}
