package com.smaile.health.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class to verify that CORS response headers are actually added to HTTP responses.
 * Uses MockMvc to test real HTTP requests and validate response headers.
 */
@WebMvcTest(controllers = CorsHttpResponseHeaderTest.TestController.class)
@Import({CorsHttpResponseHeaderTest.TestCorsConfig.class})
@ActiveProfiles("test")
class CorsHttpResponseHeaderTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testCorsPreflightRequestResponseHeaders() throws Exception {
        // Test CORS preflight request (OPTIONS) and verify all required headers are present
        mockMvc.perform(options("/api/test")
                        .header("Origin", "http://localhost:3000")
                        .header("Access-Control-Request-Method", "GET")
                        .header("Access-Control-Request-Headers", "Authorization,Content-Type"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                .andExpect(header().string("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS"))
                .andExpect(header().string("Access-Control-Allow-Headers", "Authorization,Content-Type,Accept,Origin,Cache-Control"))
                .andExpect(header().string("Access-Control-Allow-Credentials", "false"))
                .andExpect(header().string("Access-Control-Max-Age", "3600"));
    }

    @Test
    void testCorsActualGetRequestResponseHeaders() throws Exception {
        // Test actual GET request with CORS headers
        mockMvc.perform(get("/api/test")
                        .header("Origin", "http://localhost:3000"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                .andExpect(content().string("GET response"));
    }

    @Test
    void testCorsActualPostRequestResponseHeaders() throws Exception {
        // Test actual POST request with CORS headers
        mockMvc.perform(post("/api/test")
                        .header("Origin", "http://localhost:3000")
                        .header("Content-Type", "application/json")
                        .content("{\"test\": \"data\"}"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                .andExpect(content().string("POST response"));
    }

    @Test
    void testCorsWithDifferentOrigins() throws Exception {
        // Test CORS with different origins (should work with wildcard)
        String[] origins = {
                "http://localhost:3000",
                "http://localhost:8080",
                "https://example.com"
        };

        for (String origin : origins) {
            mockMvc.perform(get("/api/test")
                            .header("Origin", origin))
                    .andExpect(status().isOk())
                    .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                    .andExpect(content().string("GET response"));
        }
    }

    @Test
    void testCorsPreflightForDifferentMethods() throws Exception {
        // Test preflight requests for different HTTP methods
        String[] methods = {"GET", "POST", "PUT", "DELETE"};

        for (String method : methods) {
            mockMvc.perform(options("/api/test")
                            .header("Origin", "http://localhost:3000")
                            .header("Access-Control-Request-Method", method))
                    .andExpect(status().isOk())
                    .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                    .andExpect(header().string("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS"));
        }
    }

    @Test
    void testCorsWithCustomHeaders() throws Exception {
        // Test CORS preflight with custom headers
        mockMvc.perform(options("/api/test")
                        .header("Origin", "http://localhost:3000")
                        .header("Access-Control-Request-Method", "POST")
                        .header("Access-Control-Request-Headers", "Authorization,Content-Type,Cache-Control"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"))
                .andExpect(header().string("Access-Control-Allow-Headers", "Authorization,Content-Type,Accept,Origin,Cache-Control"));
    }

    @Test
    void testRequestWithoutOriginHeader() throws Exception {
        // Test request without Origin header (no CORS headers should be added)
        mockMvc.perform(get("/api/test"))
                .andExpect(status().isOk())
                .andExpect(header().doesNotExist("Access-Control-Allow-Origin"))
                .andExpect(content().string("GET response"));
    }

    @Test
    void testCorsHeadersAreNotAddedToNonCorsRequests() throws Exception {
        // Test that CORS headers are not added when Origin header is missing
        mockMvc.perform(post("/api/test")
                        .header("Content-Type", "application/json")
                        .content("{\"test\": \"data\"}"))
                .andExpect(status().isOk())
                .andExpect(header().doesNotExist("Access-Control-Allow-Origin"))
                .andExpect(header().doesNotExist("Access-Control-Allow-Methods"))
                .andExpect(header().doesNotExist("Access-Control-Allow-Headers"))
                .andExpect(content().string("POST response"));
    }

    @Test
    void testCorsCredentialsHeaderValue() throws Exception {
        // Test that credentials header is set to false for dev environment
        mockMvc.perform(options("/api/test")
                        .header("Origin", "http://localhost:3000")
                        .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Credentials", "false"));
    }

    @Test
    void testCorsMaxAgeHeaderValue() throws Exception {
        // Test that max-age header is set correctly
        mockMvc.perform(options("/api/test")
                        .header("Origin", "http://localhost:3000")
                        .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Max-Age", "3600"));
    }

    /**
     * Test controller to provide endpoints for CORS testing
     */
    @RestController
    @RequestMapping("/api")
    static class TestController {

        @GetMapping("/test")
        public String getTest() {
            return "GET response";
        }

        @PostMapping("/test")
        public String postTest() {
            return "POST response";
        }
    }

    /**
     * Test configuration that provides CORS configuration for testing
     */
    @TestConfiguration
    static class TestCorsConfig {

        @Bean
        public CorsConfigurationSource corsConfigurationSource() {
            CorsConfiguration configuration = new CorsConfiguration();
            
            // Configure for test environment (dev-like settings)
            configuration.addAllowedOriginPattern("*");
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
            configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "Accept", "Origin", "Cache-Control"));
            configuration.setAllowCredentials(false);
            configuration.setMaxAge(3600L);

            UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
            source.registerCorsConfiguration("/**", configuration);
            return source;
        }
    }
}
