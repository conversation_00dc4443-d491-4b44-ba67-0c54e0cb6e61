package com.smaile.health.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

/**
 * Test database configuration that provides H2 in-memory database
 * with PostgreSQL compatibility mode for testing.
 */
@TestConfiguration
@Profile("test")
public class TestDatabaseConfig {

    /**
     * Creates an H2 in-memory datasource with PostgreSQL compatibility.
     * This ensures that tests run with a database that behaves similarly to PostgreSQL
     * without requiring an actual PostgreSQL instance.
     */
    @Bean
    @Primary
    public DataSource testDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        
        // H2 with PostgreSQL compatibility mode
        dataSource.setDriverClassName("org.h2.Driver");
        dataSource.setUrl("jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH;TRACE_LEVEL_FILE=0");
        dataSource.setUsername("sa");
        dataSource.setPassword("");
        
        return dataSource;
    }
}
